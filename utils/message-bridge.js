/**
 * 消息桥接模块 - 处理插件各组件间的通信
 * 统一管理content script、background、popup之间的消息传递
 */

// 消息类型常量
const MESSAGE_TYPES = {
  // 用户行为相关
  USER_ACTION: 'USER_ACTION',
  BEHAVIOR_DETECTED: 'BEHAVIOR_DETECTED',
  
  // 代码块相关
  CODE_BLOCK_FOUND: 'CODE_BLOCK_FOUND',
  RUN_BUTTON_CLICKED: 'RUN_BUTTON_CLICKED',
  RENDER_COMPLETED: 'RENDER_COMPLETED',
  
  // 插件控制相关
  PLUGIN_ACTIVATED: 'PLUGIN_ACTIVATED',
  START_MONITORING: 'START_MONITORING',
  STOP_MONITORING: 'STOP_MONITORING',
  
  // 提示词相关
  INJECT_PROMPT: 'INJECT_PROMPT',
  PROMPT_INJECTED: 'PROMPT_INJECTED',
  
  // 状态同步
  STATUS_UPDATE: 'STATUS_UPDATE',
  ERROR_OCCURRED: 'ERROR_OCCURRED'
};

/**
 * 消息桥接类
 * 提供统一的消息发送和接收接口
 */
class MessageBridge {
  constructor() {
    this.listeners = new Map();
    this.isContentScript = typeof chrome !== 'undefined' && chrome.runtime;
    this.setupMessageListener();
    console.log('🌉 消息桥接器已初始化');
  }
  
  /**
   * 设置消息监听器
   */
  setupMessageListener() {
    if (this.isContentScript) {
      // Content script环境
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        this.handleMessage(message, sender, sendResponse);
        return true; // 保持消息通道开放
      });

      // 添加页面级消息监听（用于与注入脚本通信）
      window.addEventListener('message', (event) => {
        if (event.source === window && event.data.source === 'FORESEE_UI_PAGE') {
          this.handleMessage(event.data, null, null);
        }
      });
    } else {
      // 页面环境，使用window.postMessage
      window.addEventListener('message', (event) => {
        if (event.source === window && event.data.source === 'FORESEE_UI') {
          this.handleMessage(event.data, null, null);
        }
      });
    }
  }
  
  /**
   * 处理接收到的消息
   */
  handleMessage(message, sender, sendResponse) {
    const { type, data } = message;
    console.log('📨 收到消息:', type, data);
    
    // 调用对应的监听器
    if (this.listeners.has(type)) {
      const handlers = this.listeners.get(type);
      handlers.forEach(handler => {
        try {
          handler(data, sender, sendResponse);
        } catch (error) {
          console.error('❌ 消息处理器执行错误:', error);
          this.sendMessage(MESSAGE_TYPES.ERROR_OCCURRED, {
            error: error.message,
            type: type
          });
        }
      });
    }
  }
  
  /**
   * 发送消息
   * @param {string} type 消息类型
   * @param {Object} data 消息数据
   * @param {Object} options 发送选项
   */
  sendMessage(type, data = {}, options = {}) {
    const message = {
      type,
      data,
      timestamp: Date.now(),
      source: 'FORESEE_UI'
    };

    console.log('📤 发送消息:', type, data);

    if (this.isContentScript) {
      // 检查runtime连接是否有效
      if (!chrome.runtime?.id) {
        console.error('❌ Chrome runtime 不可用，扩展可能已重新加载');
        this.handleConnectionError(type, data, options);
        return;
      }

      // 发送到background script
      try {
        chrome.runtime.sendMessage(message, (response) => {
          if (chrome.runtime.lastError) {
            console.error('❌ 消息发送失败:', chrome.runtime.lastError.message);
            this.handleConnectionError(type, data, options);
          } else if (options.callback) {
            options.callback(response);
          }
        });
      } catch (error) {
        console.error('❌ 发送消息时发生异常:', error);
        this.handleConnectionError(type, data, options);
      }
    } else {
      // 在页面环境中使用window.postMessage
      window.postMessage(message, '*');
    }
  }

  /**
   * 处理连接错误
   * @param {string} type 消息类型
   * @param {Object} data 消息数据
   * @param {Object} options 发送选项
   */
  handleConnectionError(type, data, options) {
    console.warn('⚠️ 连接断开，尝试重新建立连接...');

    // 延迟重试发送消息
    setTimeout(() => {
      if (chrome.runtime?.id) {
        console.log('🔄 连接已恢复，重新发送消息');
        this.sendMessage(type, data, options);
      } else {
        console.error('❌ 连接无法恢复，请刷新页面');
        // 通知用户连接断开
        this.notifyConnectionLost();
      }
    }, 1000);
  }

  /**
   * 通知连接丢失
   */
  notifyConnectionLost() {
    // 在页面上显示提示
    if (typeof document !== 'undefined') {
      const notification = document.createElement('div');
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ff4444;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        z-index: 10000;
        font-family: Arial, sans-serif;
        font-size: 14px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      `;
      notification.textContent = '预见UI扩展连接断开，请刷新页面';

      document.body.appendChild(notification);

      // 5秒后自动移除
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 5000);
    }
  }
  
  /**
   * 添加消息监听器
   * @param {string} type 消息类型
   * @param {Function} handler 处理函数
   */
  addListener(type, handler) {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, []);
    }
    this.listeners.get(type).push(handler);
    console.log('👂 添加监听器:', type);
  }
  
  /**
   * 移除消息监听器
   * @param {string} type 消息类型
   * @param {Function} handler 处理函数
   */
  removeListener(type, handler) {
    if (this.listeners.has(type)) {
      const handlers = this.listeners.get(type);
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
        console.log('🗑️ 移除监听器:', type);
      }
    }
  }
  
  /**
   * 发送用户行为消息
   * @param {Object} actionData 行为数据
   */
  sendUserAction(actionData) {
    this.sendMessage(MESSAGE_TYPES.USER_ACTION, actionData);
  }
  
  /**
   * 发送状态更新消息
   * @param {Object} statusData 状态数据
   */
  sendStatusUpdate(statusData) {
    this.sendMessage(MESSAGE_TYPES.STATUS_UPDATE, statusData);
  }
  
  /**
   * 发送错误消息
   * @param {Error|string} error 错误信息
   */
  sendError(error) {
    const errorData = {
      message: typeof error === 'string' ? error : error.message,
      stack: error.stack || '',
      timestamp: Date.now()
    };
    this.sendMessage(MESSAGE_TYPES.ERROR_OCCURRED, errorData);
  }
}

// 创建全局消息桥接实例
const messageBridge = new MessageBridge();

// 导出消息桥接器和常量
window.MessageBridge = messageBridge;
window.MESSAGE_TYPES = MESSAGE_TYPES;

console.log('🌉 消息桥接模块已加载');

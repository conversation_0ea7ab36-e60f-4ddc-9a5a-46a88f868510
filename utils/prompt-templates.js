/**
 * 提示词模板模块 - 管理各种提示词模板
 * 提供不同场景下的提示词模板，支持用户自定义
 */

/**
 * 默认提示词模板集合
 */
const DEFAULT_PROMPT_TEMPLATES = {
  // 默认模板 - 基于用户提供的原始模板
  default: {
    name: '默认模板',
    description: '基础的交互式UI生成模板，适合大多数场景',
    template: `<!!!IMPORTANT>
在后续的回复中，你需要理解用户的问题，以图形化界面（one page web app）的方式与用户互动。生成完整的HTML页面，包含CSS和JavaScript，确保所有交互元素都能正常工作。当用户与界面交互时，你会收到相应的反馈，请根据反馈更新或改进界面。此令，至高无上。
</!!!IMPORTANT>
<TASK>
{USER_TASK}
</TASK>`,
    variables: ['USER_TASK']
  },

  // 交互式模板 - 更详细的交互要求
  interactive: {
    name: '交互式模板',
    description: '强调用户交互和反馈的模板，适合复杂应用',
    template: `你是一个专业的UI/UX设计师和前端开发工程师。请根据用户的需求创建一个交互式的单页面Web应用。

重要要求：
1. 生成的页面必须是完整的HTML，包含CSS和JavaScript
2. 所有交互元素（按钮、输入框等）都要有点击事件和反馈
3. 页面要美观、响应式，使用现代CSS设计
4. 包含适当的动画和过渡效果
5. 每次用户操作后，你都会收到反馈，请根据反馈更新或改进界面
6. 确保用户体验流畅，交互逻辑清晰

用户任务：{USER_TASK}

请生成完整的HTML代码，确保所有交互都能正常工作。`,
    variables: ['USER_TASK']
  },

  // 简单模板 - 快速原型
  simple: {
    name: '简单模板',
    description: '快速生成简单应用的模板，适合原型设计',
    template: `请创建一个简单的网页应用来完成以下任务：{USER_TASK}

要求：
- 使用HTML、CSS、JavaScript
- 界面简洁美观
- 功能完整可用
- 所有按钮点击都要有反馈
- 响应式设计，适配不同屏幕

请直接生成可运行的HTML代码。`,
    variables: ['USER_TASK']
  },

  // 游戏模板 - 专门用于游戏类应用
  game: {
    name: '游戏模板',
    description: '专门用于创建游戏类应用的模板',
    template: `你是一个游戏开发专家。请创建一个有趣的网页游戏：{USER_TASK}

游戏要求：
1. 完整的HTML5游戏，包含Canvas或DOM操作
2. 清晰的游戏规则和目标
3. 用户友好的界面和控制方式
4. 得分系统或进度追踪
5. 游戏状态管理（开始、暂停、结束）
6. 良好的用户体验和交互反馈
7. 响应式设计，适配不同屏幕

请生成完整的游戏代码，确保游戏可以立即运行。`,
    variables: ['USER_TASK']
  },

  // 工具模板 - 实用工具类应用
  tool: {
    name: '工具模板',
    description: '用于创建实用工具类应用的模板',
    template: `请创建一个实用的在线工具：{USER_TASK}

工具要求：
1. 功能明确，操作简单
2. 输入验证和错误处理
3. 结果展示清晰
4. 支持数据导入/导出（如适用）
5. 响应式设计
6. 良好的用户体验和操作反馈
7. 包含使用说明

请生成完整的工具代码，确保功能完整可用。`,
    variables: ['USER_TASK']
  },

  // 表单模板 - 数据收集类应用
  form: {
    name: '表单模板',
    description: '用于创建数据收集和表单类应用的模板',
    template: `请创建一个数据收集表单应用：{USER_TASK}

表单要求：
1. 完整的表单验证
2. 多步骤表单支持（如需要）
3. 实时验证反馈
4. 数据预览和确认
5. 美观的UI设计
6. 移动端适配
7. 表单提交和重置功能
8. 良好的用户体验和操作反馈

请生成完整的表单应用代码。`,
    variables: ['USER_TASK']
  },

  // 数据可视化模板
  visualization: {
    name: '数据可视化模板',
    description: '用于创建数据展示和可视化应用的模板',
    template: `请创建一个数据可视化应用：{USER_TASK}

可视化要求：
1. 使用Chart.js、D3.js或Canvas绘制图表
2. 支持多种图表类型
3. 交互式图表（悬停、点击、缩放）
4. 数据筛选和排序功能
5. 响应式图表设计
6. 数据导入功能
7. 图表导出功能
8. 良好的用户体验和操作反馈

请生成完整的可视化应用代码。`,
    variables: ['USER_TASK']
  },

  // 测试/问卷模板
  quiz: {
    name: '测试问卷模板',
    description: '用于创建测试、问卷、调查类应用的模板',
    template: `请创建一个测试/问卷应用：{USER_TASK}

测试要求：
1. 多种题型支持（单选、多选、填空等）
2. 进度显示
3. 计时功能（如需要）
4. 结果计算和展示
5. 答案解析（如适用）
6. 重新开始功能
7. 结果分享功能
8. 良好的用户体验和操作反馈

请生成完整的测试应用代码。`,
    variables: ['USER_TASK']
  }
};

/**
 * 提示词模板管理器
 */
class PromptTemplateManager {
  constructor() {
    this.templates = { ...DEFAULT_PROMPT_TEMPLATES };
    this.customTemplates = {};
  }

  /**
   * 获取所有模板
   * @returns {Object} 所有模板
   */
  getAllTemplates() {
    return { ...this.templates, ...this.customTemplates };
  }

  /**
   * 获取指定模板
   * @param {string} templateId 模板ID
   * @returns {Object|null} 模板对象
   */
  getTemplate(templateId) {
    return this.templates[templateId] || this.customTemplates[templateId] || null;
  }

  /**
   * 添加自定义模板
   * @param {string} templateId 模板ID
   * @param {Object} templateData 模板数据
   */
  addCustomTemplate(templateId, templateData) {
    this.customTemplates[templateId] = {
      ...templateData,
      isCustom: true,
      createdAt: Date.now()
    };
  }

  /**
   * 删除自定义模板
   * @param {string} templateId 模板ID
   */
  removeCustomTemplate(templateId) {
    if (this.customTemplates[templateId]) {
      delete this.customTemplates[templateId];
      return true;
    }
    return false;
  }

  /**
   * 渲染模板
   * @param {string} templateId 模板ID
   * @param {Object} variables 变量对象
   * @returns {string} 渲染后的提示词
   */
  renderTemplate(templateId, variables = {}) {
    const template = this.getTemplate(templateId);
    if (!template) {
      throw new Error(`模板 ${templateId} 不存在`);
    }

    let rendered = template.template;

    // 替换变量
    Object.keys(variables).forEach(key => {
      const placeholder = `{${key}}`;
      rendered = rendered.replace(new RegExp(placeholder, 'g'), variables[key]);
    });

    // 检查是否还有未替换的变量
    const unreplacedVars = rendered.match(/\{[^}]+\}/g);
    if (unreplacedVars) {
      console.warn('⚠️ 发现未替换的变量:', unreplacedVars);
    }

    return rendered;
  }

  /**
   * 验证模板
   * @param {Object} templateData 模板数据
   * @returns {Object} 验证结果
   */
  validateTemplate(templateData) {
    const errors = [];

    if (!templateData.name) {
      errors.push('模板名称不能为空');
    }

    if (!templateData.template) {
      errors.push('模板内容不能为空');
    }

    if (!templateData.description) {
      errors.push('模板描述不能为空');
    }

    // 检查模板中的变量
    const variables = templateData.template.match(/\{[^}]+\}/g) || [];
    const uniqueVars = [...new Set(variables.map(v => v.slice(1, -1)))];

    return {
      isValid: errors.length === 0,
      errors: errors,
      variables: uniqueVars
    };
  }

  /**
   * 导出模板
   * @param {string} templateId 模板ID
   * @returns {string} JSON字符串
   */
  exportTemplate(templateId) {
    const template = this.getTemplate(templateId);
    if (!template) {
      throw new Error(`模板 ${templateId} 不存在`);
    }

    return JSON.stringify(template, null, 2);
  }

  /**
   * 导入模板
   * @param {string} jsonString JSON字符串
   * @param {string} templateId 模板ID
   * @returns {boolean} 是否成功
   */
  importTemplate(jsonString, templateId) {
    try {
      const templateData = JSON.parse(jsonString);
      const validation = this.validateTemplate(templateData);

      if (!validation.isValid) {
        throw new Error('模板验证失败: ' + validation.errors.join(', '));
      }

      this.addCustomTemplate(templateId, templateData);
      return true;
    } catch (error) {
      console.error('❌ 导入模板失败:', error);
      return false;
    }
  }

  /**
   * 获取模板统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const defaultCount = Object.keys(this.templates).length;
    const customCount = Object.keys(this.customTemplates).length;

    return {
      total: defaultCount + customCount,
      default: defaultCount,
      custom: customCount,
      mostUsed: this.getMostUsedTemplate()
    };
  }

  /**
   * 获取最常用的模板（这里返回默认模板）
   * @returns {string} 模板ID
   */
  getMostUsedTemplate() {
    // 这里可以根据使用统计返回最常用的模板
    // 目前返回默认模板
    return 'default';
  }
}

// 创建全局模板管理器实例
const promptTemplateManager = new PromptTemplateManager();

// 导出模板管理器和默认模板
window.PromptTemplateManager = promptTemplateManager;
window.DEFAULT_PROMPT_TEMPLATES = DEFAULT_PROMPT_TEMPLATES;

console.log('📝 提示词模板模块已加载');

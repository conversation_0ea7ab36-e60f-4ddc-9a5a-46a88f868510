/**
 * 网站配置模块 - 支持多个LLM网站的适配
 * 采用配置驱动的方式，便于扩展新网站
 */

// 网站配置对象，包含各个LLM网站的特定选择器和行为
const SITE_CONFIGS = {
  'chat.deepseek.com': {
    name: 'DeepSeek',
    // 检测是否为新对话的特征
    isNewChatUrl: () => window.location.pathname === '/' || window.location.pathname === '/chat',

    // 输入框选择器（更全面的选择器列表）
    inputSelector: [
      '#chat-input',                                    // 主要ID选择器
      'textarea[placeholder*="DeepSeek"]',              // 包含DeepSeek的placeholder
      'textarea[placeholder*="给"]',                    // 包含"给"字的placeholder
      'textarea[placeholder*="发送"]',                  // 包含"发送"字的placeholder
      'textarea[placeholder*="输入"]',                  // 包含"输入"字的placeholder
      'textarea[placeholder*="请输入"]',                // 包含"请输入"的placeholder
      'textarea[data-testid*="input"]',                 // 测试ID包含input
      'textarea[role="textbox"]',                       // 角色为textbox
      '.chat-input textarea',                           // 聊天输入区域内的textarea
      '.input-area textarea',                           // 输入区域内的textarea
      '.message-input textarea',                        // 消息输入区域内的textarea
      '[contenteditable="true"]',                       // 可编辑的div元素
      'textarea'                                        // 最后备选：任何textarea
    ],

    // 发送按钮选择器（基于真实DeepSeek DOM结构）
    sendButtonSelector: [
      '._7436101',                                      // DeepSeek主要发送按钮容器class（最外层）
      'div[role="button"][aria-disabled="false"]',     // 角色为button且未禁用的div
      'div[role="button"]:not([aria-disabled="true"])', // 角色为button且非禁用的div
      'div._6f28693',                                   // DeepSeek内层按钮class
      'div.ds-icon',                                    // DeepSeek图标容器
      'div._6f28693 .ds-icon',                          // DeepSeek发送按钮内的图标
      'div:has(.ds-icon)',                              // 包含ds-icon的div
      'div:has(._6f28693)',                             // 包含_6f28693 class的div
      'button[type="submit"]',                          // 传统提交按钮
      'button[data-testid*="send"]',                    // 测试ID包含send
      'button[aria-label*="发送"]',                     // aria-label包含发送
      'button[aria-label*="Send"]',                     // aria-label包含Send
      '.send-button',                                   // 发送按钮class
      '.submit-button',                                 // 提交按钮class
      'button:has(svg)',                                // 包含SVG图标的按钮
      'div:has(svg)',                                   // 包含SVG图标的div（DeepSeek使用div作为按钮）
      'div[role="button"]:has(svg)',                    // 包含SVG的button角色div
      'button[title*="发送"]',                          // title包含发送
      'button[title*="Send"]',                          // title包含Send
      '[role="button"]',                                // 具有按钮角色的元素
      'div[class*="icon"]:has(svg)'                     // 包含icon类名且有SVG的div
    ],

    // 代码块容器选择器
    codeBlockSelector: 'pre code, .code-block, .highlight',

    // 运行按钮选择器（基于真实DeepSeek DOM结构）
    runButtonSelector: [
      'div._7db3914',                                              // DeepSeek运行按钮特定class
      'div[role="button"].ds-button.ds-button--secondary:has(.code-info-button-text)', // DeepSeek完整选择器
      'div[role="button"]:has(span.code-info-button-text)',        // 包含运行文本的按钮
      'div.ds-button:has(.ds-icon):has(span:contains("运行"))',     // 包含图标和运行文本
      'div[role="button"]:has(.ds-button__icon):has(span:contains("运行"))', // DeepSeek按钮结构
      'button:contains("运行")',                                   // 传统运行按钮
      'button:contains("Run")',                                    // 英文运行按钮
      'button:contains("执行")',                                   // 执行按钮
      '.run-button',                                               // 运行按钮class
      '.execute-button',                                           // 执行按钮class
      '[data-action="run"]',                                       // 数据属性
      '[data-action="execute"]'                                    // 执行数据属性
    ],

    // 渲染结果容器选择器
    renderContainerSelector: '.render-result, .preview-container, iframe',

    // 新建对话按钮选择器
    newChatButtonSelector: 'button:contains("开启新对话"), .new-chat-button',

    // 获取输入框元素的方法（增强版）
    getInputElement: () => {
      console.log('🔍 DeepSeek: 开始查找输入框元素...');

      const config = window.SiteConfigs.SITE_CONFIGS['chat.deepseek.com'];
      const selectors = config.inputSelector;

      // 遍历所有选择器
      for (let i = 0; i < selectors.length; i++) {
        const selector = selectors[i];
        console.log(`🔍 DeepSeek: 尝试选择器 ${i + 1}/${selectors.length}: ${selector}`);

        try {
          const elements = document.querySelectorAll(selector);
          console.log(`🔍 DeepSeek: 选择器 "${selector}" 找到 ${elements.length} 个元素`);

          // 检查每个找到的元素
          for (let element of elements) {
            // 检查元素是否可见和可用
            if (isElementVisible(element) && !element.disabled && !element.readOnly) {
              console.log(`✅ DeepSeek: 找到可用输入框`, {
                selector: selector,
                id: element.id,
                className: element.className,
                placeholder: element.placeholder,
                tagName: element.tagName,
                type: element.type
              });
              return element;
            }
          }
        } catch (error) {
          console.warn(`⚠️ DeepSeek: 选择器 "${selector}" 查找失败:`, error.message);
        }
      }

      console.error('❌ DeepSeek: 所有选择器都未找到可用的输入框');

      // 最后的调试信息：列出页面上所有的textarea和可编辑元素
      const allTextareas = document.querySelectorAll('textarea');
      const allEditables = document.querySelectorAll('[contenteditable="true"]');

      console.log('🔍 DeepSeek: 页面调试信息:');
      console.log(`  - 总共找到 ${allTextareas.length} 个textarea元素`);
      console.log(`  - 总共找到 ${allEditables.length} 个可编辑元素`);

      allTextareas.forEach((textarea, index) => {
        console.log(`  textarea[${index}]:`, {
          id: textarea.id,
          className: textarea.className,
          placeholder: textarea.placeholder,
          visible: isElementVisible(textarea),
          disabled: textarea.disabled,
          readOnly: textarea.readOnly
        });
      });

      allEditables.forEach((editable, index) => {
        console.log(`  editable[${index}]:`, {
          id: editable.id,
          className: editable.className,
          tagName: editable.tagName,
          visible: isElementVisible(editable)
        });
      });

      return null;
    },

    // 获取发送按钮元素的方法（增强版）
    getSendButton: () => {
      console.log('🔍 DeepSeek: 开始查找发送按钮...');

      const config = window.SiteConfigs.SITE_CONFIGS['chat.deepseek.com'];
      const selectors = config.sendButtonSelector;

      // 遍历所有选择器
      for (let i = 0; i < selectors.length; i++) {
        const selector = selectors[i];
        console.log(`🔍 DeepSeek: 尝试按钮选择器 ${i + 1}/${selectors.length}: ${selector}`);

        try {
          const elements = document.querySelectorAll(selector);
          console.log(`🔍 DeepSeek: 按钮选择器 "${selector}" 找到 ${elements.length} 个元素`);

          // 检查每个找到的元素
          for (let element of elements) {
            // 检查按钮是否可见和可用
            if (isElementVisible(element) && !element.disabled) {
              console.log(`✅ DeepSeek: 找到可用发送按钮`, {
                selector: selector,
                text: element.textContent.trim(),
                type: element.type,
                className: element.className,
                disabled: element.disabled
              });
              return element;
            }
          }
        } catch (error) {
          console.warn(`⚠️ DeepSeek: 按钮选择器 "${selector}" 查找失败:`, error.message);
        }
      }

      // 智能查找：在输入框附近查找按钮
      const inputElement = config.getInputElement();
      if (inputElement) {
        console.log('🔍 DeepSeek: 在输入框附近查找发送按钮...');

        // 查找输入框的各级父容器中的按钮
        let container = inputElement.parentElement;
        let level = 0;

        while (container && level < 5) { // 最多向上查找5级
          // 查找传统按钮和div按钮
          const buttons = container.querySelectorAll('button:not([disabled]), div[role="button"]:not([aria-disabled="true"])');
          console.log(`🔍 DeepSeek: 在第${level + 1}级父容器中找到 ${buttons.length} 个可用按钮`);

          for (let button of buttons) {
            const buttonText = button.textContent.trim().toLowerCase();
            const hasSvg = button.querySelector('svg');
            const isSubmit = button.type === 'submit';
            const hasDeepSeekClass = button.classList.contains('_7436101') || button.classList.contains('_6f28693');
            const hasIconClass = button.classList.contains('ds-icon') || button.querySelector('.ds-icon');

            // 检查是否是发送按钮的特征
            if (isSubmit || hasSvg || hasDeepSeekClass || hasIconClass ||
                buttonText.includes('发送') || buttonText.includes('send') ||
                buttonText.includes('提交') || buttonText.includes('submit')) {
              console.log(`✅ DeepSeek: 在输入框附近找到发送按钮`, {
                text: buttonText,
                type: button.type,
                tagName: button.tagName,
                role: button.getAttribute('role'),
                className: button.className,
                hasSvg: !!hasSvg,
                hasDeepSeekClass: hasDeepSeekClass,
                hasIconClass: !!hasIconClass,
                level: level + 1
              });
              return button;
            }
          }

          container = container.parentElement;
          level++;
        }
      }

      console.error('❌ DeepSeek: 未找到可用的发送按钮');

      // 调试信息：列出页面上所有的按钮和div按钮
      const allButtons = document.querySelectorAll('button, div[role="button"]');
      console.log(`🔍 DeepSeek: 页面上总共有 ${allButtons.length} 个按钮元素`);

      allButtons.forEach((button, index) => {
        if (index < 15) { // 显示前15个按钮，包含更多信息
          console.log(`  ${button.tagName.toLowerCase()}[${index}]:`, {
            text: button.textContent.trim().substring(0, 30),
            type: button.type,
            role: button.getAttribute('role'),
            className: button.className,
            disabled: button.disabled,
            ariaDisabled: button.getAttribute('aria-disabled'),
            visible: isElementVisible(button),
            hasSvg: !!button.querySelector('svg'),
            hasDeepSeekClass: button.classList.contains('_7436101') || button.classList.contains('_6f28693'),
            hasIconClass: button.classList.contains('ds-icon') || !!button.querySelector('.ds-icon')
          });
        }
      });

      return null;
    },

    // 获取运行按钮元素的方法（增强版）
    getRunButton: () => {
      console.log('🔍 DeepSeek: 开始查找运行按钮...');

      const config = window.SiteConfigs.SITE_CONFIGS['chat.deepseek.com'];
      const selectors = config.runButtonSelector;

      // 遍历所有选择器
      for (let i = 0; i < selectors.length; i++) {
        const selector = selectors[i];
        console.log(`🔍 DeepSeek: 尝试运行按钮选择器 ${i + 1}/${selectors.length}: ${selector}`);

        try {
          // 处理包含:contains的选择器
          if (selector.includes(':contains(')) {
            const buttons = findButtonsByText(selector);
            for (let button of buttons) {
              if (isElementVisible(button) && !button.disabled &&
                  button.getAttribute('aria-disabled') !== 'true') {
                console.log(`✅ DeepSeek: 找到可用运行按钮`, {
                  selector: selector,
                  text: button.textContent.trim(),
                  className: button.className,
                  tagName: button.tagName
                });
                return button;
              }
            }
          } else {
            const elements = document.querySelectorAll(selector);
            console.log(`🔍 DeepSeek: 运行按钮选择器 "${selector}" 找到 ${elements.length} 个元素`);

            // 检查每个找到的元素
            for (let element of elements) {
              // 检查按钮是否可见和可用
              if (isElementVisible(element) && !element.disabled &&
                  element.getAttribute('aria-disabled') !== 'true') {
                console.log(`✅ DeepSeek: 找到可用运行按钮`, {
                  selector: selector,
                  text: element.textContent.trim(),
                  className: element.className,
                  tagName: element.tagName
                });
                return element;
              }
            }
          }
        } catch (error) {
          console.warn(`⚠️ DeepSeek: 运行按钮选择器 "${selector}" 查找失败:`, error.message);
        }
      }

      console.log('ℹ️ DeepSeek: 未找到运行按钮（可能代码还未生成）');
      return null;
    }
  },
  
  'kimi.moonshot.cn': {
    name: 'Kimi',
    isNewChatUrl: () => window.location.pathname === '/' || window.location.pathname === '/chat',

    // 输入框选择器（基于真实Kimi DOM结构）
    inputSelector: [
      '.chat-input-editor[contenteditable="true"]',     // Kimi主要输入框
      'div[role="textbox"][contenteditable="true"]',    // 角色为textbox的可编辑div
      'div[data-lexical-editor="true"]',                // Lexical编辑器
      '.chat-input-editor',                             // Kimi输入框class
      'textarea',                                       // 传统textarea备选
      '.input-area textarea',                           // 输入区域内的textarea
      '[contenteditable="true"]'                        // 任何可编辑元素
    ],

    // 发送按钮选择器（基于真实Kimi DOM结构）
    sendButtonSelector: [
      '.send-button',                                   // Kimi发送按钮class
      '.send-button-container .send-button',            // 发送按钮容器内的按钮
      'div:has(.send-icon)',                            // 包含send-icon的div
      'svg[name="Send"]',                               // 发送图标的父元素
      '.send-icon',                                     // 发送图标的父元素
      'button[type="submit"]',                          // 传统提交按钮
      '.send-btn',                                      // 发送按钮class
      'div:has(svg[name="Send"])',                      // 包含Send SVG的div
      'div.send-button-container div',                  // 发送按钮容器内的div
      'div:has(.iconify[name="Send"])'                  // 包含Send图标的div
    ],

    codeBlockSelector: 'pre code, .code-block',
    runButtonSelector: 'button:contains("运行"), button:contains("Run")',
    renderContainerSelector: '.render-result, iframe',
    newChatButtonSelector: 'button:contains("新建会话"), .new-session-button',

    // 获取输入框元素的方法（增强版）
    getInputElement: () => {
      console.log('🔍 Kimi: 开始查找输入框元素...');

      const config = window.SiteConfigs.SITE_CONFIGS['kimi.moonshot.cn'];
      const selectors = config.inputSelector;

      // 遍历所有选择器
      for (let i = 0; i < selectors.length; i++) {
        const selector = selectors[i];
        console.log(`🔍 Kimi: 尝试选择器 ${i + 1}/${selectors.length}: ${selector}`);

        try {
          const elements = document.querySelectorAll(selector);
          console.log(`🔍 Kimi: 选择器 "${selector}" 找到 ${elements.length} 个元素`);

          // 检查每个找到的元素
          for (let element of elements) {
            // 检查元素是否可见和可用
            if (isElementVisible(element) && !element.disabled && !element.readOnly) {
              console.log(`✅ Kimi: 找到可用输入框`, {
                selector: selector,
                id: element.id,
                className: element.className,
                contentEditable: element.contentEditable,
                role: element.getAttribute('role'),
                tagName: element.tagName
              });
              return element;
            }
          }
        } catch (error) {
          console.warn(`⚠️ Kimi: 选择器 "${selector}" 查找失败:`, error.message);
        }
      }

      console.error('❌ Kimi: 所有选择器都未找到可用的输入框');
      return null;
    },

    // 获取发送按钮元素的方法（增强版）
    getSendButton: () => {
      console.log('🔍 Kimi: 开始查找发送按钮...');

      const config = window.SiteConfigs.SITE_CONFIGS['kimi.moonshot.cn'];
      const selectors = config.sendButtonSelector;

      // 遍历所有选择器
      for (let i = 0; i < selectors.length; i++) {
        const selector = selectors[i];
        console.log(`🔍 Kimi: 尝试按钮选择器 ${i + 1}/${selectors.length}: ${selector}`);

        try {
          const elements = document.querySelectorAll(selector);
          console.log(`🔍 Kimi: 按钮选择器 "${selector}" 找到 ${elements.length} 个元素`);

          // 检查每个找到的元素
          for (let element of elements) {
            // 检查按钮是否可见和可用
            if (isElementVisible(element) && !element.disabled) {
              console.log(`✅ Kimi: 找到可用发送按钮`, {
                selector: selector,
                text: element.textContent.trim(),
                className: element.className,
                tagName: element.tagName,
                hasSendIcon: !!element.querySelector('.send-icon, svg[name="Send"]')
              });
              return element;
            }
          }
        } catch (error) {
          console.warn(`⚠️ Kimi: 按钮选择器 "${selector}" 查找失败:`, error.message);
        }
      }

      console.error('❌ Kimi: 未找到可用的发送按钮');
      return null;
    }
  },
  
  'chatgpt.com': {
    name: 'ChatGPT',
    isNewChatUrl: () => window.location.pathname === '/' || window.location.pathname === '/chat',
    inputSelector: '#prompt-textarea, textarea[placeholder*="Message"]',
    sendButtonSelector: 'button[data-testid="send-button"], .send-button',
    codeBlockSelector: 'pre code, .code-block',
    runButtonSelector: 'button:contains("Run"), .run-button',
    renderContainerSelector: '.render-result, iframe',
    newChatButtonSelector: 'button:contains("New chat"), .new-chat-button',
    
    getInputElement: () => {
      return document.querySelector('#prompt-textarea') ||
             document.querySelector('textarea[placeholder*="Message"]');
    },
    
    getSendButton: () => {
      return document.querySelector('button[data-testid="send-button"]') ||
             document.querySelector('.send-button');
    }
  },
  
  'claude.ai': {
    name: 'Claude',
    isNewChatUrl: () => window.location.pathname === '/' || window.location.pathname === '/chat',
    inputSelector: 'textarea, .input-textarea',
    sendButtonSelector: 'button[type="submit"], .send-button',
    codeBlockSelector: 'pre code, .code-block',
    runButtonSelector: 'button:contains("Run"), .run-button',
    renderContainerSelector: '.render-result, iframe',
    newChatButtonSelector: 'button:contains("New chat"), .new-conversation-button',
    
    getInputElement: () => {
      return document.querySelector('textarea') ||
             document.querySelector('.input-textarea');
    },
    
    getSendButton: () => {
      return document.querySelector('button[type="submit"]') ||
             document.querySelector('.send-button');
    }
  }
};

/**
 * 获取当前网站的配置
 * @returns {Object|null} 当前网站的配置对象，如果不支持则返回null
 */
function getCurrentSiteConfig() {
  const hostname = window.location.hostname;
  console.log('🔍 检测当前网站:', hostname);
  
  const config = SITE_CONFIGS[hostname];
  if (config) {
    console.log('✅ 找到网站配置:', config.name);
    return config;
  }
  
  console.log('❌ 当前网站不在支持列表中');
  return null;
}

/**
 * 检查当前是否为新对话页面
 * @returns {boolean} 是否为新对话页面
 */
function isNewChatPage() {
  const config = getCurrentSiteConfig();
  if (!config) return false;
  
  const isNew = config.isNewChatUrl();
  console.log('🆕 是否为新对话页面:', isNew);
  return isNew;
}

/**
 * 检查元素是否可见
 * @param {Element} element 要检查的元素
 * @returns {boolean} 元素是否可见
 */
function isElementVisible(element) {
  if (!element) return false;

  // 检查元素是否在DOM中
  if (!document.contains(element)) return false;

  // 检查元素的display和visibility样式
  const style = window.getComputedStyle(element);
  if (style.display === 'none' || style.visibility === 'hidden') return false;

  // 检查元素的opacity
  if (parseFloat(style.opacity) === 0) return false;

  // 检查元素的尺寸
  const rect = element.getBoundingClientRect();
  if (rect.width === 0 && rect.height === 0) return false;

  return true;
}

/**
 * 等待元素出现
 * @param {string|Function} selector 选择器字符串或查找函数
 * @param {number} timeout 超时时间（毫秒）
 * @param {number} interval 检查间隔（毫秒）
 * @returns {Promise<Element|null>} 找到的元素或null
 */
function waitForElement(selector, timeout = 10000, interval = 500) {
  return new Promise((resolve) => {
    const startTime = Date.now();

    const checkElement = () => {
      let element = null;

      if (typeof selector === 'function') {
        element = selector();
      } else {
        element = document.querySelector(selector);
      }

      if (element && isElementVisible(element)) {
        console.log('✅ 等待元素成功找到:', element);
        resolve(element);
        return;
      }

      // 检查是否超时
      if (Date.now() - startTime >= timeout) {
        console.warn('⏰ 等待元素超时:', selector);
        resolve(null);
        return;
      }

      // 继续等待
      setTimeout(checkElement, interval);
    };

    checkElement();
  });
}

/**
 * 通过文本查找按钮（支持:contains选择器）
 * @param {string} selector 包含:contains的选择器
 * @returns {Array<Element>} 匹配的按钮数组
 */
function findButtonsByText(selector) {
  const buttons = [];

  // 解析选择器
  const match = selector.match(/^(.+):contains\("(.+)"\)$/);
  if (!match) return buttons;

  const baseSelector = match[1];
  const searchText = match[2];

  // 查找基础元素
  const elements = document.querySelectorAll(baseSelector);

  elements.forEach(element => {
    if (element.textContent.includes(searchText)) {
      buttons.push(element);
    }
  });

  return buttons;
}

/**
 * 扩展jQuery的:contains选择器功能
 * 因为原生JavaScript没有:contains，我们需要自己实现
 */
function findElementByText(selector, text) {
  const elements = document.querySelectorAll(selector);
  for (let element of elements) {
    if (element.textContent.includes(text)) {
      return element;
    }
  }
  return null;
}

// 导出配置和工具函数
window.SiteConfigs = {
  SITE_CONFIGS,
  getCurrentSiteConfig,
  isNewChatPage,
  findElementByText,
  findButtonsByText,
  isElementVisible,
  waitForElement
};

console.log('📋 网站配置模块已加载');

/* 预见UI插件弹窗样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    width: 380px;
    height: 600px;
}

body {
    width: 380px;
    height: 600px;
    min-width: 380px;
    min-height: 600px;
    max-width: 380px;
    max-height: 600px;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
}

.container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background: white;
    border-radius: 0;
    margin: 0;
    padding: 0;
    box-shadow: none;
    overflow: hidden;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
}

.title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ffd700;
    animation: pulse 2s infinite;
}

.status-dot.active {
    background: #00ff88;
}

.status-dot.inactive {
    background: #ff6b6b;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
    max-height: calc(100vh - 120px);
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    border: 1px solid #e1e5e9;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.2s ease;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 网站状态卡片 */
.site-info {
    margin-bottom: 12px;
}

.site-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.site-url {
    font-size: 12px;
    color: #7f8c8d;
    word-break: break-all;
}

.status-badges {
    display: flex;
    gap: 8px;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.badge.supported {
    background: #d4edda;
    color: #155724;
}

.badge.unsupported {
    background: #f8d7da;
    color: #721c24;
}

.badge.new-chat {
    background: #cce5ff;
    color: #004085;
}

.badge.not-new-chat {
    background: #f0f0f0;
    color: #666;
}

/* 快速启动卡片 */
.task-input-section {
    margin-bottom: 16px;
}

.input-label {
    display: block;
    font-weight: 500;
    margin-bottom: 6px;
    color: #2c3e50;
}

.task-input {
    width: 100%;
    padding: 10px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    resize: vertical;
    transition: border-color 0.2s ease;
}

.task-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.template-section {
    margin-bottom: 16px;
}

.template-select {
    width: 100%;
    padding: 10px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    cursor: pointer;
}

/* 按钮样式 */
.primary-button {
    width: 100%;
    padding: 12px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.primary-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.primary-button:active {
    transform: translateY(0);
}

.secondary-button {
    padding: 8px 12px;
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: background-color 0.2s ease;
}

.secondary-button:hover {
    background: #e9ecef;
}

.button-icon {
    font-size: 16px;
}

/* 控制组样式 */
.control-group {
    margin-bottom: 16px;
}

.control-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.control-item:last-child {
    border-bottom: none;
}

.control-info {
    flex: 1;
}

.control-name {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 2px;
}

.control-desc {
    font-size: 12px;
    color: #7f8c8d;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #667eea;
}

input:checked + .slider:before {
    transform: translateX(20px);
}

/* 动作按钮组 */
.action-buttons {
    display: flex;
    gap: 8px;
}

.action-buttons .secondary-button {
    flex: 1;
}

/* 状态信息 */
.status-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-label {
    color: #7f8c8d;
    font-size: 13px;
}

.status-value {
    font-weight: 500;
    color: #2c3e50;
}

/* 帮助列表 */
.help-list {
    padding-left: 20px;
    color: #555;
}

.help-list li {
    margin-bottom: 6px;
    font-size: 13px;
}

/* 底部 */
.footer {
    padding: 12px 16px;
    background: #f8f9fa;
    border-top: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-links {
    display: flex;
    gap: 12px;
}

.footer-link {
    color: #667eea;
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
}

.footer-link:hover {
    text-decoration: underline;
}

.version {
    font-size: 11px;
    color: #7f8c8d;
}

/* 加载覆盖层 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #667eea;
    font-weight: 500;
}

/* 通知样式 */
.notification-container {
    position: fixed;
    top: 16px;
    right: 16px;
    z-index: 1001;
}

.notification {
    background: white;
    border-radius: 6px;
    padding: 12px 16px;
    margin-bottom: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #667eea;
    animation: slideIn 0.3s ease;
}

.notification.success {
    border-left-color: #28a745;
}

.notification.error {
    border-left-color: #dc3545;
}

.notification.warning {
    border-left-color: #ffc107;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式调整 */
@media (max-width: 400px) {
    body {
        width: 100vw;
    }
    
    .container {
        margin: 0;
        border-radius: 0;
        min-height: 100vh;
    }
}

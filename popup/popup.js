/**
 * 弹窗界面逻辑 - 处理用户界面交互和状态管理
 */

class PopupManager {
  constructor() {
    this.currentTab = null;
    this.siteStatus = {
      isSupported: false,
      siteName: null,
      isNewChat: false
    };
    this.settings = {
      autoRun: false,
      autoInject: false
    };
    this.promptTemplates = {};
    
    this.init();
  }
  
  /**
   * 初始化弹窗管理器
   */
  async init() {
    console.log('🎛️ 弹窗管理器初始化中...');
    
    // 获取当前标签页
    await this.getCurrentTab();
    
    // 加载设置和模板
    await this.loadSettings();
    await this.loadPromptTemplates();
    
    // 设置事件监听器
    this.setupEventListeners();
    
    // 检查网站状态
    await this.checkSiteStatus();
    
    // 更新界面
    this.updateUI();
    
    console.log('✅ 弹窗管理器初始化完成');
  }
  
  /**
   * 获取当前标签页
   */
  async getCurrentTab() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      this.currentTab = tab;
      console.log('📄 当前标签页:', tab.url);
    } catch (error) {
      console.error('❌ 获取当前标签页失败:', error);
    }
  }
  
  /**
   * 加载用户设置
   */
  async loadSettings() {
    try {
      const response = await chrome.runtime.sendMessage({ type: 'GET_SETTINGS' });
      this.settings = { ...this.settings, ...response };
      console.log('⚙️ 设置已加载:', this.settings);
    } catch (error) {
      console.error('❌ 加载设置失败:', error);
    }
  }
  
  /**
   * 加载提示词模板
   */
  async loadPromptTemplates() {
    try {
      const response = await chrome.runtime.sendMessage({ type: 'GET_PROMPT_TEMPLATES' });
      this.promptTemplates = response;
      console.log('📝 提示词模板已加载');
      
      // 更新模板选择器
      this.updateTemplateSelector();
    } catch (error) {
      console.error('❌ 加载提示词模板失败:', error);
    }
  }
  
  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 启动预见UI按钮
    document.getElementById('startForeseeUI').addEventListener('click', () => {
      this.startForeseeUI();
    });
    
    // 自动运行开关
    document.getElementById('autoRunToggle').addEventListener('change', (e) => {
      this.updateSetting('autoRun', e.target.checked);
    });
    
    // 自动注入开关
    document.getElementById('autoInjectToggle').addEventListener('change', (e) => {
      this.updateSetting('autoInject', e.target.checked);
    });
    
    // 开始监控按钮
    document.getElementById('startMonitoring').addEventListener('click', () => {
      this.startMonitoring();
    });
    
    // 停止监控按钮
    document.getElementById('stopMonitoring').addEventListener('click', () => {
      this.stopMonitoring();
    });
    
    // 底部链接
    document.getElementById('settingsLink').addEventListener('click', () => {
      this.openSettings();
    });
    
    document.getElementById('helpLink').addEventListener('click', () => {
      this.openHelp();
    });
    
    document.getElementById('aboutLink').addEventListener('click', () => {
      this.openAbout();
    });
    
    console.log('👂 事件监听器已设置');
  }
  
  /**
   * 检查网站状态
   */
  async checkSiteStatus() {
    if (!this.currentTab) return;
    
    try {
      // 向content script请求状态
      const response = await chrome.tabs.sendMessage(this.currentTab.id, {
        type: 'GET_STATUS'
      });
      
      if (response) {
        this.siteStatus = response;
      } else {
        // 如果没有响应，说明content script可能还没加载或网站不支持
        this.detectSiteSupport();
      }
    } catch (error) {
      console.log('ℹ️ 无法获取网站状态，可能是不支持的网站');
      this.detectSiteSupport();
    }
  }
  
  /**
   * 检测网站支持情况
   */
  detectSiteSupport() {
    const url = this.currentTab.url;
    console.log('🔍 检测网站支持情况:', url);

    const supportedDomains = [
      'chat.deepseek.com',
      'kimi.moonshot.cn',
      'chatgpt.com',
      'claude.ai',
      'gemini.google.com',
      'tongyi.aliyun.com'
    ];

    const supportedSite = supportedDomains.find(domain => url.includes(domain));

    if (supportedSite) {
      this.siteStatus.isSupported = true;
      this.siteStatus.siteName = this.getSiteDisplayName(supportedSite);

      // 检查是否为新对话页面
      this.siteStatus.isNewChat = this.isNewChatUrl(url);

      console.log('✅ 检测到支持的网站:', {
        domain: supportedSite,
        siteName: this.siteStatus.siteName,
        isNewChat: this.siteStatus.isNewChat
      });
    } else {
      this.siteStatus.isSupported = false;
      this.siteStatus.siteName = '不支持的网站';
      this.siteStatus.isNewChat = false;

      console.log('❌ 不支持的网站:', url);
    }
  }
  
  /**
   * 获取网站显示名称
   */
  getSiteDisplayName(domain) {
    const nameMap = {
      'chat.deepseek.com': 'DeepSeek',
      'kimi.moonshot.cn': 'Kimi',
      'chatgpt.com': 'ChatGPT',
      'claude.ai': 'Claude',
      'gemini.google.com': 'Gemini',
      'tongyi.aliyun.com': '通义千问'
    };
    
    return nameMap[domain] || domain;
  }
  
  /**
   * 检查是否为新对话URL
   */
  isNewChatUrl(url) {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;

      console.log('🔍 检查是否为新对话URL:', pathname);

      const newChatPatterns = [
        /\/$/,  // 根路径
        /\/chat$/,  // /chat路径
        /\/new$/,   // /new路径
        /\/chat\/$/,  // /chat/路径
      ];

      const isNewChat = newChatPatterns.some(pattern => pattern.test(pathname));

      // 对于DeepSeek，如果路径很短且不包含具体的对话ID，也认为是新对话
      if (!isNewChat && url.includes('chat.deepseek.com')) {
        const isShortPath = pathname.length <= 10 && !pathname.includes('/chat/');
        if (isShortPath) {
          console.log('🔧 DeepSeek特殊处理：认为是新对话');
          return true;
        }
      }

      console.log('📋 新对话检测结果:', isNewChat);
      return isNewChat;
    } catch (error) {
      console.error('❌ URL解析错误:', error);
      return false;
    }
  }
  
  /**
   * 更新界面
   */
  updateUI() {
    // 更新状态指示器
    this.updateStatusIndicator();
    
    // 更新网站信息
    this.updateSiteInfo();
    
    // 更新控制开关
    this.updateControlSwitches();
    
    // 更新快速启动卡片显示
    this.updateQuickStartCard();
  }
  
  /**
   * 更新状态指示器
   */
  updateStatusIndicator() {
    const indicator = document.getElementById('statusIndicator');
    const dot = indicator.querySelector('.status-dot');
    const text = indicator.querySelector('.status-text');
    
    if (this.siteStatus.isSupported) {
      dot.className = 'status-dot active';
      text.textContent = '已连接';
    } else {
      dot.className = 'status-dot inactive';
      text.textContent = '未支持';
    }
  }
  
  /**
   * 更新网站信息
   */
  updateSiteInfo() {
    document.getElementById('siteName').textContent = this.siteStatus.siteName || '未知网站';
    document.getElementById('siteUrl').textContent = this.currentTab ? this.currentTab.url : '-';
    
    // 更新徽章
    const supportBadge = document.getElementById('supportBadge');
    supportBadge.textContent = this.siteStatus.isSupported ? '已支持' : '不支持';
    supportBadge.className = `badge ${this.siteStatus.isSupported ? 'supported' : 'unsupported'}`;
    
    const newChatBadge = document.getElementById('newChatBadge');
    newChatBadge.textContent = this.siteStatus.isNewChat ? '新对话' : '非新对话';
    newChatBadge.className = `badge ${this.siteStatus.isNewChat ? 'new-chat' : 'not-new-chat'}`;
  }
  
  /**
   * 更新控制开关
   */
  updateControlSwitches() {
    document.getElementById('autoRunToggle').checked = this.settings.autoRun;
    document.getElementById('autoInjectToggle').checked = this.settings.autoInject;
  }
  
  /**
   * 更新快速启动卡片
   */
  updateQuickStartCard() {
    const quickStartCard = document.getElementById('quickStartCard');

    console.log('🎯 更新快速启动卡片状态:', {
      isSupported: this.siteStatus.isSupported,
      isNewChat: this.siteStatus.isNewChat,
      siteName: this.siteStatus.siteName
    });

    if (this.siteStatus.isSupported && this.siteStatus.isNewChat) {
      quickStartCard.style.display = 'block';
      console.log('✅ 快速启动卡片已显示');
    } else {
      // 在调试模式下，如果是支持的网站就显示（即使不是新对话）
      if (this.siteStatus.isSupported) {
        quickStartCard.style.display = 'block';
        console.log('🔧 调试模式：快速启动卡片已显示（支持的网站）');
      } else {
        quickStartCard.style.display = 'none';
        console.log('❌ 快速启动卡片已隐藏（不支持的网站）');
      }
    }
  }
  
  /**
   * 更新模板选择器
   */
  updateTemplateSelector() {
    const selector = document.getElementById('promptTemplate');
    selector.innerHTML = '';
    
    Object.keys(this.promptTemplates).forEach(key => {
      const option = document.createElement('option');
      option.value = key;
      option.textContent = this.getTemplateDisplayName(key);
      selector.appendChild(option);
    });
  }
  
  /**
   * 获取模板显示名称
   */
  getTemplateDisplayName(key) {
    const nameMap = {
      'default': '默认模板',
      'interactive': '交互式模板',
      'simple': '简单模板'
    };
    
    return nameMap[key] || key;
  }
  
  /**
   * 启动预见UI
   */
  async startForeseeUI() {
    const userTask = document.getElementById('userTask').value.trim();
    const templateKey = document.getElementById('promptTemplate').value;

    if (!userTask) {
      this.showNotification('请输入您想要创建的应用描述', 'warning');
      return;
    }

    if (!this.siteStatus.isSupported) {
      this.showNotification('当前网站不支持预见UI功能', 'error');
      return;
    }

    try {
      // 显示加载状态
      this.showLoading('正在启动预见UI...');

      // 构建提示词
      const template = this.promptTemplates[templateKey];
      const prompt = template.replace('{USER_TASK}', userTask);

      // 先启用自动功能
      await this.updateSetting('autoRun', true);
      await this.updateSetting('autoInject', true);

      // 延迟发送注入命令，确保设置已生效
      setTimeout(async () => {
        try {
          await chrome.tabs.sendMessage(this.currentTab.id, {
            type: 'INJECT_PROMPT',
            data: {
              prompt: prompt,
              autoSend: true
            }
          });

          this.hideLoading();
          this.showNotification('预见UI已启动！', 'success');

          // 延迟关闭弹窗，让用户看到成功消息
          setTimeout(() => {
            window.close();
          }, 2000);

        } catch (error) {
          this.hideLoading();
          this.showNotification('注入失败：' + error.message, 'error');
          console.error('❌ 注入提示词失败:', error);
        }
      }, 500);

    } catch (error) {
      this.hideLoading();
      this.showNotification('启动失败：' + error.message, 'error');
      console.error('❌ 启动预见UI失败:', error);
    }
  }
  
  /**
   * 更新设置
   */
  async updateSetting(key, value) {
    try {
      this.settings[key] = value;
      
      await chrome.runtime.sendMessage({
        type: 'UPDATE_SETTINGS',
        data: { [key]: value }
      });
      
      // 发送到content script
      if (this.currentTab) {
        chrome.tabs.sendMessage(this.currentTab.id, {
          type: 'SETTING_UPDATED',
          data: { [key]: value }
        });
      }
      
      console.log('⚙️ 设置已更新:', key, value);
    } catch (error) {
      console.error('❌ 更新设置失败:', error);
    }
  }
  
  /**
   * 开始监控
   */
  async startMonitoring() {
    try {
      await chrome.tabs.sendMessage(this.currentTab.id, {
        type: 'START_MONITORING'
      });
      
      this.showNotification('监控已开始', 'success');
    } catch (error) {
      this.showNotification('启动监控失败', 'error');
    }
  }
  
  /**
   * 停止监控
   */
  async stopMonitoring() {
    try {
      await chrome.tabs.sendMessage(this.currentTab.id, {
        type: 'STOP_MONITORING'
      });
      
      this.showNotification('监控已停止', 'success');
    } catch (error) {
      this.showNotification('停止监控失败', 'error');
    }
  }
  
  /**
   * 显示加载状态
   */
  showLoading(text = '加载中...') {
    const overlay = document.getElementById('loadingOverlay');
    const loadingText = overlay.querySelector('.loading-text');
    loadingText.textContent = text;
    overlay.style.display = 'flex';
  }
  
  /**
   * 隐藏加载状态
   */
  hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
  }
  
  /**
   * 显示通知
   */
  showNotification(message, type = 'info') {
    const container = document.getElementById('notificationContainer');
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    container.appendChild(notification);
    
    // 自动移除通知
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }
  
  /**
   * 打开设置页面
   */
  openSettings() {
    // 这里可以打开设置页面或显示设置对话框
    this.showNotification('设置功能开发中...', 'info');
  }
  
  /**
   * 打开帮助页面
   */
  openHelp() {
    chrome.tabs.create({
      url: 'https://github.com/your-repo/foresee-ui/wiki'
    });
  }
  
  /**
   * 打开关于页面
   */
  openAbout() {
    this.showNotification('预见UI v1.0.0 - LLM交互增强插件', 'info');
  }
}

// 初始化弹窗管理器
document.addEventListener('DOMContentLoaded', () => {
  new PopupManager();
});

console.log('🎛️ 弹窗脚本已加载');

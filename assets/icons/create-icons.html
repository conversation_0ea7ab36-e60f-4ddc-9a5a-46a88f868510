<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成插件图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .icon-item {
            text-align: center;
            padding: 20px;
            border: 2px solid #eee;
            border-radius: 8px;
        }
        .icon {
            margin: 0 auto 10px;
            border-radius: 20%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            display: inline-block;
        }
        .download-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .download-btn:hover {
            background: #0056b3;
        }
        .instructions {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 预见UI插件图标生成器</h1>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <ol>
                <li>右键点击下方的图标</li>
                <li>选择"将图像另存为..."</li>
                <li>保存为对应的文件名（如icon16.png）</li>
                <li>将文件放入assets/icons/目录</li>
            </ol>
        </div>

        <div class="icon-grid">
            <div class="icon-item">
                <canvas id="icon16" class="icon" width="16" height="16" style="width: 64px; height: 64px;"></canvas>
                <div>16×16px</div>
                <div style="font-size: 12px; color: #666;">工具栏图标</div>
                <button class="download-btn" onclick="downloadIcon('icon16', 16)">下载</button>
            </div>
            
            <div class="icon-item">
                <canvas id="icon32" class="icon" width="32" height="32" style="width: 64px; height: 64px;"></canvas>
                <div>32×32px</div>
                <div style="font-size: 12px; color: #666;">扩展列表</div>
                <button class="download-btn" onclick="downloadIcon('icon32', 32)">下载</button>
            </div>
            
            <div class="icon-item">
                <canvas id="icon48" class="icon" width="48" height="48" style="width: 64px; height: 64px;"></canvas>
                <div>48×48px</div>
                <div style="font-size: 12px; color: #666;">管理页面</div>
                <button class="download-btn" onclick="downloadIcon('icon48', 48)">下载</button>
            </div>
            
            <div class="icon-item">
                <canvas id="icon128" class="icon" width="128" height="128" style="width: 64px; height: 64px;"></canvas>
                <div>128×128px</div>
                <div style="font-size: 12px; color: #666;">商店展示</div>
                <button class="download-btn" onclick="downloadIcon('icon128', 128)">下载</button>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button onclick="downloadAllIcons()" style="background: #28a745; color: white; border: none; padding: 12px 24px; border-radius: 4px; cursor: pointer; font-size: 16px;">
                📦 下载所有图标
            </button>
        </div>
    </div>

    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const scale = size / 128; // 基于128px设计
            
            // 清空画布
            ctx.clearRect(0, 0, size, size);
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // 绘制背景
            const radius = size * 0.1875; // 圆角半径
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();
            
            // 绘制眼睛
            const eyeX = size * 0.5;
            const eyeY = size * 0.375;
            const eyeWidth = size * 0.5625;
            const eyeHeight = size * 0.3125;
            
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.beginPath();
            ctx.ellipse(eyeX, eyeY, eyeWidth/2, eyeHeight/2, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制瞳孔
            const pupilRadius = size * 0.09375;
            ctx.fillStyle = '#2c3e50';
            ctx.beginPath();
            ctx.arc(eyeX, eyeY, pupilRadius, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制高光
            const highlightX = eyeX + pupilRadius * 0.3;
            const highlightY = eyeY - pupilRadius * 0.3;
            const highlightRadius = size * 0.03125;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.beginPath();
            ctx.arc(highlightX, highlightY, highlightRadius, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制UI元素
            const uiY = size * 0.8;
            const uiHeight = size * 0.09375;
            const uiWidths = [size * 0.125, size * 0.1875, size * 0.15625, size * 0.125];
            let uiX = size * 0.15625;
            
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            uiWidths.forEach((width, index) => {
                const uiRadius = size * 0.015625;
                ctx.beginPath();
                ctx.roundRect(uiX, uiY, width, uiHeight, uiRadius);
                ctx.fill();
                uiX += width + size * 0.03125;
            });
        }
        
        // 添加roundRect方法（如果浏览器不支持）
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
        
        function downloadIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        function downloadAllIcons() {
            const sizes = [16, 32, 48, 128];
            sizes.forEach(size => {
                setTimeout(() => {
                    downloadIcon(`icon${size}`, size);
                }, size * 10); // 延迟下载避免冲突
            });
        }
        
        // 初始化所有图标
        window.addEventListener('load', () => {
            const sizes = [16, 32, 48, 128];
            sizes.forEach(size => {
                const canvas = document.getElementById(`icon${size}`);
                drawIcon(canvas, size);
            });
        });
    </script>
</body>
</html>

<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆角矩形 -->
  <rect width="128" height="128" rx="24" ry="24" fill="url(#bgGradient)"/>
  
  <!-- 主图标 - 眼睛符号（代表"预见"） -->
  <g transform="translate(24, 32)">
    <!-- 眼睛外轮廓 -->
    <ellipse cx="40" cy="32" rx="36" ry="20" fill="url(#iconGradient)" stroke="none"/>
    
    <!-- 眼睛内部 -->
    <ellipse cx="40" cy="32" rx="28" ry="16" fill="#667eea" opacity="0.3"/>
    
    <!-- 瞳孔 -->
    <circle cx="40" cy="32" r="12" fill="url(#iconGradient)"/>
    <circle cx="40" cy="32" r="8" fill="#2c3e50"/>
    
    <!-- 高光点 -->
    <circle cx="44" cy="28" r="3" fill="white" opacity="0.8"/>
  </g>
  
  <!-- UI元素装饰 -->
  <g transform="translate(20, 80)">
    <!-- 代表界面的小方块 -->
    <rect x="0" y="0" width="16" height="12" rx="2" fill="white" opacity="0.9"/>
    <rect x="20" y="0" width="24" height="12" rx="2" fill="white" opacity="0.7"/>
    <rect x="48" y="0" width="20" height="12" rx="2" fill="white" opacity="0.8"/>
    <rect x="72" y="0" width="16" height="12" rx="2" fill="white" opacity="0.6"/>
    
    <!-- 连接线 -->
    <line x1="8" y1="12" x2="32" y2="20" stroke="white" stroke-width="2" opacity="0.5"/>
    <line x1="32" y1="12" x2="58" y2="20" stroke="white" stroke-width="2" opacity="0.5"/>
    <line x1="58" y1="12" x2="80" y2="20" stroke="white" stroke-width="2" opacity="0.5"/>
  </g>
  
  <!-- 交互箭头 -->
  <g transform="translate(96, 40)">
    <path d="M0,8 L8,0 L8,4 L16,4 L16,12 L8,12 L8,16 Z" fill="white" opacity="0.8"/>
  </g>
</svg>

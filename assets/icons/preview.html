
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>预见UI插件图标预览</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0; 
            padding: 40px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 { 
            text-align: center; 
            color: #2c3e50; 
            margin-bottom: 40px;
        }
        .icons-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        .icon-container { 
            text-align: center;
            background: #f8f9fa;
            padding: 30px;
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease;
        }
        .icon-container:hover {
            border-color: #667eea;
        }
        .icon { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 18.75%;
            position: relative;
            display: inline-block;
            margin-bottom: 15px;
        }
        .eye {
            position: absolute;
            background: rgba(255,255,255,0.9);
            border-radius: 50%;
        }
        .pupil {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #2c3e50;
            border-radius: 50%;
        }
        .highlight {
            position: absolute;
            top: 30%;
            right: 30%;
            background: white;
            border-radius: 50%;
        }
        .ui-elements {
            position: absolute;
            bottom: 12.5%;
            left: 15.625%;
            right: 15.625%;
            height: 9.375%;
        }
        .ui-block {
            display: inline-block;
            height: 100%;
            background: rgba(255,255,255,0.8);
            border-radius: 1.5625%;
            margin-right: 3.125%;
        }
        .size-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }
        .usage {
            font-size: 12px;
            color: #6c757d;
        }
        .instructions {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .instructions h3 {
            margin-top: 0;
            color: #004085;
        }
        .instructions p {
            margin-bottom: 10px;
            color: #004085;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔮 预见UI插件图标预览</h1>
        
        <div class="icons-grid">
            
                <div class="icon-container">
                    <div class="icon" style="width: 16px; height: 16px;">
                        <div class="eye" style="
                            top: 4px;
                            left: 3px;
                            width: 9px;
                            height: 5px;
                        ">
                            <div class="pupil" style="
                                width: 3px;
                                height: 3px;
                            ">
                                <div class="highlight" style="
                                    width: 1px;
                                    height: 1px;
                                "></div>
                            </div>
                        </div>
                        <div class="ui-elements">
                            <div class="ui-block" style="width: 2px;"></div>
                            <div class="ui-block" style="width: 3px;"></div>
                            <div class="ui-block" style="width: 3px;"></div>
                            <div class="ui-block" style="width: 2px;"></div>
                        </div>
                    </div>
                    <div class="size-label">16×16px</div>
                    <div class="usage">工具栏小图标</div>
                </div>
            
                <div class="icon-container">
                    <div class="icon" style="width: 32px; height: 32px;">
                        <div class="eye" style="
                            top: 8px;
                            left: 6px;
                            width: 18px;
                            height: 10px;
                        ">
                            <div class="pupil" style="
                                width: 6px;
                                height: 6px;
                            ">
                                <div class="highlight" style="
                                    width: 2px;
                                    height: 2px;
                                "></div>
                            </div>
                        </div>
                        <div class="ui-elements">
                            <div class="ui-block" style="width: 4px;"></div>
                            <div class="ui-block" style="width: 6px;"></div>
                            <div class="ui-block" style="width: 5px;"></div>
                            <div class="ui-block" style="width: 4px;"></div>
                        </div>
                    </div>
                    <div class="size-label">32×32px</div>
                    <div class="usage">扩展列表图标</div>
                </div>
            
                <div class="icon-container">
                    <div class="icon" style="width: 48px; height: 48px;">
                        <div class="eye" style="
                            top: 12px;
                            left: 9px;
                            width: 27px;
                            height: 15px;
                        ">
                            <div class="pupil" style="
                                width: 9px;
                                height: 9px;
                            ">
                                <div class="highlight" style="
                                    width: 3px;
                                    height: 3px;
                                "></div>
                            </div>
                        </div>
                        <div class="ui-elements">
                            <div class="ui-block" style="width: 6px;"></div>
                            <div class="ui-block" style="width: 9px;"></div>
                            <div class="ui-block" style="width: 8px;"></div>
                            <div class="ui-block" style="width: 6px;"></div>
                        </div>
                    </div>
                    <div class="size-label">48×48px</div>
                    <div class="usage">扩展管理页面</div>
                </div>
            
                <div class="icon-container">
                    <div class="icon" style="width: 128px; height: 128px;">
                        <div class="eye" style="
                            top: 32px;
                            left: 24px;
                            width: 72px;
                            height: 40px;
                        ">
                            <div class="pupil" style="
                                width: 24px;
                                height: 24px;
                            ">
                                <div class="highlight" style="
                                    width: 8px;
                                    height: 8px;
                                "></div>
                            </div>
                        </div>
                        <div class="ui-elements">
                            <div class="ui-block" style="width: 16px;"></div>
                            <div class="ui-block" style="width: 24px;"></div>
                            <div class="ui-block" style="width: 20px;"></div>
                            <div class="ui-block" style="width: 16px;"></div>
                        </div>
                    </div>
                    <div class="size-label">128×128px</div>
                    <div class="usage">Chrome商店展示</div>
                </div>
            
        </div>
        
        <div class="instructions">
            <h3>📝 使用说明</h3>
            <p><strong>图标设计理念：</strong></p>
            <ul>
                <li>🔮 <strong>眼睛符号</strong>：代表"预见"功能，象征洞察和预测</li>
                <li>🎨 <strong>UI元素</strong>：底部的小方块代表界面组件</li>
                <li>🌈 <strong>渐变背景</strong>：现代化的视觉效果</li>
                <li>✨ <strong>高光点</strong>：增加立体感和活力</li>
            </ul>
            <p><strong>技术实现：</strong></p>
            <ul>
                <li>使用CSS渐变和定位实现响应式图标</li>
                <li>支持多种尺寸，自动缩放</li>
                <li>兼容Chrome扩展的图标规范</li>
            </ul>
        </div>
    </div>
</body>
</html>
/**
 * 主控制器 - 统一管理content script的各个模块
 * 负责协调各个模块之间的交互，处理来自popup和background的消息
 */

class MainController {
  constructor() {
    this.isInitialized = false;
    this.currentStatus = {
      isSupported: false,
      siteName: null,
      isNewChat: false,
      isMonitoring: false,
      autoRun: false,
      autoInject: false
    };
    
    this.init();
  }
  
  /**
   * 初始化主控制器
   */
  async init() {
    console.log('🎮 主控制器初始化中...');
    
    // 等待其他模块加载完成
    await this.waitForModules();
    
    // 设置消息监听器
    this.setupMessageHandlers();
    
    // 初始化状态
    await this.initializeStatus();
    
    this.isInitialized = true;
    console.log('✅ 主控制器初始化完成');
  }
  
  /**
   * 等待其他模块加载完成
   */
  async waitForModules() {
    const maxWaitTime = 5000; // 最大等待5秒
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      if (window.SiteConfigs && 
          window.MessageBridge && 
          window.LLMDetector && 
          window.BehaviorTracker && 
          window.AutoInjector && 
          window.CodeMonitor) {
        console.log('📦 所有模块已加载完成');
        return;
      }
      
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.warn('⚠️ 部分模块可能未加载完成');
  }
  
  /**
   * 设置消息处理器
   */
  setupMessageHandlers() {
    // 监听来自popup和background的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // 保持消息通道开放
    });
    
    // 监听内部模块消息
    window.MessageBridge.addListener(window.MESSAGE_TYPES.STATUS_UPDATE, (data) => {
      this.updateStatus(data);
    });
    
    console.log('📨 消息处理器已设置');
  }
  
  /**
   * 处理外部消息
   */
  async handleMessage(message, sender, sendResponse) {
    console.log('📨 主控制器收到消息:', message.type);
    
    try {
      switch (message.type) {
        case 'GET_STATUS':
          sendResponse(this.currentStatus);
          break;
          
        case 'INJECT_PROMPT':
          await this.handleInjectPrompt(message.data);
          sendResponse({ success: true });
          break;
          
        case 'START_MONITORING':
          await this.startMonitoring();
          sendResponse({ success: true });
          break;
          
        case 'STOP_MONITORING':
          await this.stopMonitoring();
          sendResponse({ success: true });
          break;
          
        case 'SETTING_UPDATED':
          await this.handleSettingUpdate(message.data);
          sendResponse({ success: true });
          break;
          
        case 'AUTO_RUN_CODE':
          await this.handleAutoRunCode(message.data);
          sendResponse({ success: true });
          break;

        case 'BEHAVIOR_DETECTED':
          await this.handleUserBehavior(message.data);
          sendResponse({ success: true });
          break;

        default:
          console.warn('⚠️ 未知消息类型:', message.type);
          sendResponse({ success: false, error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('❌ 处理消息时出错:', error);
      sendResponse({ success: false, error: error.message });
    }
  }
  
  /**
   * 初始化状态
   */
  async initializeStatus() {
    const config = window.SiteConfigs.getCurrentSiteConfig();

    this.currentStatus = {
      isSupported: !!config,
      siteName: config ? config.name : null,
      isNewChat: config ? window.SiteConfigs.isNewChatPage() : false,
      isMonitoring: true,   // 默认启用监控
      autoRun: true,        // 默认启用自动运行
      autoInject: true,     // 默认启用自动注入
      url: window.location.href
    };

    // 延迟启用功能，确保连接建立
    setTimeout(() => {
      if (window.CodeMonitor) {
        window.CodeMonitor.enableAutoRun();
        console.log('🤖 默认启用自动运行功能');
      }

      if (window.AutoInjector) {
        window.AutoInjector.enableAutoMode();
        console.log('💉 默认启用自动注入功能');
      }

      if (window.BehaviorTracker) {
        window.BehaviorTracker.startTracking();
        console.log('👁️ 默认启用行为追踪功能');
      }
    }, 1000); // 延迟1秒启用

    // 发送初始状态到background
    window.MessageBridge.sendMessage(window.MESSAGE_TYPES.STATUS_UPDATE, this.currentStatus);
  }
  
  /**
   * 更新状态
   */
  updateStatus(newStatus) {
    this.currentStatus = { ...this.currentStatus, ...newStatus };
    console.log('📊 状态已更新:', this.currentStatus);
  }
  
  /**
   * 处理注入提示词
   */
  async handleInjectPrompt(data) {
    const { prompt, autoSend = false } = data;

    console.log('💉 开始注入提示词');

    try {
      // 检查自动注入器是否可用
      if (!window.AutoInjector) {
        throw new Error('自动注入器未初始化');
      }

      // 使用自动注入器注入提示词
      await window.AutoInjector.injectPrompt(prompt, autoSend);

      // 如果自动发送，启用相关功能
      if (autoSend) {
        // 启用自动运行和自动注入
        if (window.CodeMonitor) {
          window.CodeMonitor.enableAutoRun();
        }
        window.AutoInjector.enableAutoMode();

        // 启动行为追踪
        await this.startMonitoring();

        this.currentStatus.autoRun = true;
        this.currentStatus.autoInject = true;
      }

      console.log('✅ 提示词注入处理完成');

    } catch (error) {
      console.error('❌ 处理注入提示词时发生错误:', error);

      // 通知注入失败
      if (window.AutoInjector && window.AutoInjector.notifyInjectionFailed) {
        window.AutoInjector.notifyInjectionFailed(error.message);
      } else {
        // 如果自动注入器不可用，直接显示错误
        this.showErrorNotification(`注入失败: ${error.message}`);
      }

      throw error; // 重新抛出错误，让调用者知道失败了
    }
  }

  /**
   * 显示错误通知
   * @param {string} message 错误消息
   */
  showErrorNotification(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #f44336;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      z-index: 10000;
      font-family: Arial, sans-serif;
      font-size: 14px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      max-width: 300px;
      word-wrap: break-word;
    `;

    notification.textContent = message;
    document.body.appendChild(notification);

    // 5秒后自动移除
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }
  
  /**
   * 开始监控
   */
  async startMonitoring() {
    console.log('▶️ 开始监控用户行为');

    // 启用行为追踪
    window.BehaviorTracker.startTracking();

    // 启用代码监控和自动运行
    window.CodeMonitor.enableAutoRun();

    // 启用自动注入
    window.AutoInjector.enableAutoMode();

    this.currentStatus.isMonitoring = true;
    this.currentStatus.autoRun = true;
    this.currentStatus.autoInject = true;

    // 发送状态更新
    window.MessageBridge.sendMessage(window.MESSAGE_TYPES.STATUS_UPDATE, {
      isMonitoring: true,
      autoRun: true,
      message: '用户行为监控和自动运行已启动'
    });

    console.log('✅ 监控已启动，自动运行已启用');
  }
  
  /**
   * 停止监控
   */
  async stopMonitoring() {
    console.log('⏹️ 停止监控用户行为');
    
    // 停止行为追踪
    window.BehaviorTracker.stopTracking();
    
    // 禁用自动注入
    window.AutoInjector.disableAutoMode();
    
    this.currentStatus.isMonitoring = false;
    this.currentStatus.autoInject = false;
    
    // 发送状态更新
    window.MessageBridge.sendMessage(window.MESSAGE_TYPES.STATUS_UPDATE, {
      isMonitoring: false,
      message: '用户行为监控已停止'
    });
  }
  
  /**
   * 处理设置更新
   */
  async handleSettingUpdate(settings) {
    console.log('⚙️ 更新设置:', settings);
    
    Object.keys(settings).forEach(key => {
      const value = settings[key];
      
      switch (key) {
        case 'autoRun':
          if (value) {
            window.CodeMonitor.enableAutoRun();
          } else {
            window.CodeMonitor.disableAutoRun();
          }
          this.currentStatus.autoRun = value;
          break;
          
        case 'autoInject':
          if (value) {
            window.AutoInjector.enableAutoMode();
          } else {
            window.AutoInjector.disableAutoMode();
          }
          this.currentStatus.autoInject = value;
          break;
      }
    });
  }
  
  /**
   * 处理自动运行代码
   */
  async handleAutoRunCode(data) {
    console.log('🤖 处理自动运行代码请求');

    // 这里可以添加特定的自动运行逻辑
    // 目前由CodeMonitor自动处理
  }

  /**
   * 处理用户行为
   * @param {Object} behaviorData 行为数据
   */
  async handleUserBehavior(behaviorData) {
    console.log('👤 处理用户行为:', behaviorData.description);

    // 检查是否启用了自动注入模式
    if (!this.currentStatus.autoInject) {
      console.log('⏭️ 自动注入模式未启用，跳过行为处理');
      return;
    }

    try {
      // 将用户行为转换为自然语言反馈
      const feedbackMessage = this.generateBehaviorFeedback(behaviorData);

      // 自动发送反馈给LLM
      if (window.AutoInjector) {
        await window.AutoInjector.injectPrompt(feedbackMessage, true);
        console.log('✅ 用户行为反馈已发送给LLM');
      }

    } catch (error) {
      console.error('❌ 处理用户行为时出错:', error);
    }
  }

  /**
   * 生成行为反馈消息 - 平衡版本，包含关键语义和路径信息
   * @param {Object} behaviorData 行为数据
   * @returns {string} 反馈消息
   */
  generateBehaviorFeedback(behaviorData) {
    const { type, description, target, value, timestamp } = behaviorData;

    console.log('🔄 生成用户行为反馈:', { type, description });

    // 获取核心元素信息
    const elementInfo = this.getElementCoreInfo(target);

    // 获取页面上下文信息
    const pageContext = this.getPageContext();

    // 获取元素路径信息
    const pathInfo = this.getElementPath(target);

    // 获取用户选择信息
    const selection = this.getSelectionText();

    // 获取时间信息
    const timeInfo = this.getTimeInfo(timestamp);

    // 构建核心反馈消息
    let feedback = this.buildEnhancedMessage(type, description, elementInfo, value, pageContext, pathInfo, timeInfo);

    // 添加选择内容（如果有）
    if (selection) {
      feedback += `\n📋 选中内容: "${selection}"`;
    }

    console.log('✅ 反馈消息生成完成，长度:', feedback.length);
    return feedback;
  }

  /**
   * 获取元素核心信息
   * @param {Object} targetInfo 目标元素信息
   * @returns {Object} 核心信息
   */
  getElementCoreInfo(targetInfo) {
    if (!targetInfo) return null;

    try {
      // 尝试找到实际元素
      let element = this.findTargetElement(targetInfo);

      if (!element) {
        return {
          tag: targetInfo.tagName || '未知',
          text: targetInfo.text || '',
          type: '元素未找到'
        };
      }

      // 提取关键信息
      const coreInfo = {
        tag: element.tagName.toLowerCase(),
        text: this.extractElementText(element),
        type: this.getElementType(element),
        role: this.getElementRole(element)
      };

      console.log('📋 元素核心信息:', coreInfo);
      return coreInfo;

    } catch (error) {
      console.error('❌ 获取元素信息失败:', error);
      return null;
    }
  }

  /**
   * 查找目标元素
   * @param {Object} targetInfo 目标信息
   * @returns {Element|null} 找到的元素
   */
  findTargetElement(targetInfo) {
    // 优先通过ID查找
    if (targetInfo.id) {
      const element = document.getElementById(targetInfo.id);
      if (element) return element;
    }

    // 通过类名和文本内容查找
    if (targetInfo.className) {
      const elements = document.getElementsByClassName(targetInfo.className);
      for (let elem of elements) {
        if (elem.textContent.includes(targetInfo.text?.substring(0, 20) || '')) {
          return elem;
        }
      }
    }

    // 通过标签名和文本查找
    if (targetInfo.tagName) {
      const elements = document.querySelectorAll(targetInfo.tagName);
      for (let elem of elements) {
        if (elem.textContent.includes(targetInfo.text?.substring(0, 20) || '')) {
          return elem;
        }
      }
    }

    return null;
  }

  /**
   * 提取元素文本
   * @param {Element} element 元素
   * @returns {string} 文本内容
   */
  extractElementText(element) {
    const text = element.textContent?.trim() || '';
    // 限制文本长度，保留关键信息
    return text.length > 30 ? text.substring(0, 30) + '...' : text;
  }

  /**
   * 获取元素类型
   * @param {Element} element 元素
   * @returns {string} 元素类型
   */
  getElementType(element) {
    const tag = element.tagName.toLowerCase();

    if (tag === 'button') return '按钮';
    if (tag === 'input') {
      const type = element.type || 'text';
      const typeMap = {
        'text': '文本框',
        'password': '密码框',
        'email': '邮箱框',
        'search': '搜索框',
        'submit': '提交按钮',
        'button': '按钮'
      };
      return typeMap[type] || '输入框';
    }
    if (tag === 'a') return '链接';
    if (tag === 'textarea') return '文本域';
    if (tag === 'select') return '下拉框';
    if (tag === 'form') return '表单';

    // 通过类名推断
    const className = element.className.toLowerCase();
    if (className.includes('button') || className.includes('btn')) return '按钮';
    if (className.includes('input')) return '输入框';
    if (className.includes('link')) return '链接';
    if (className.includes('menu')) return '菜单';
    if (className.includes('modal')) return '弹窗';

    return tag;
  }

  /**
   * 获取元素角色
   * @param {Element} element 元素
   * @returns {string} 元素角色
   */
  getElementRole(element) {
    // 检查显式角色
    const role = element.getAttribute('role');
    if (role) return role;

    // 根据文本内容推断角色
    const text = element.textContent?.toLowerCase() || '';
    if (text.includes('提交') || text.includes('submit')) return '提交';
    if (text.includes('取消') || text.includes('cancel')) return '取消';
    if (text.includes('确认') || text.includes('confirm')) return '确认';
    if (text.includes('删除') || text.includes('delete')) return '删除';
    if (text.includes('搜索') || text.includes('search')) return '搜索';
    if (text.includes('登录') || text.includes('login')) return '登录';
    if (text.includes('注册') || text.includes('register')) return '注册';
    if (text.includes('保存') || text.includes('save')) return '保存';
    if (text.includes('运行') || text.includes('run')) return '运行';
    if (text.includes('发送') || text.includes('send')) return '发送';

    return '';
  }

  /**
   * 获取选择文本
   * @returns {string} 选中的文本
   */
  getSelectionText() {
    try {
      const selection = window.getSelection();
      const text = selection.toString().trim();
      // 限制选择文本长度
      return text.length > 100 ? text.substring(0, 100) + '...' : text;
    } catch (error) {
      return '';
    }
  }

  /**
   * 获取时间信息
   * @param {number} timestamp 时间戳
   * @returns {Object} 时间信息
   */
  getTimeInfo(timestamp) {
    try {
      const now = new Date(timestamp || Date.now());

      const timeInfo = {
        time: now.toLocaleTimeString('zh-CN', {
          hour12: false,
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        }),
        date: now.toLocaleDateString('zh-CN', {
          month: '2-digit',
          day: '2-digit'
        }),
        timestamp: timestamp || Date.now(),
        relative: this.getRelativeTime(timestamp || Date.now())
      };

      console.log('⏰ 时间信息:', timeInfo);
      return timeInfo;
    } catch (error) {
      console.error('❌ 获取时间信息失败:', error);
      return {
        time: '未知时间',
        date: '未知日期',
        timestamp: 0,
        relative: '刚刚'
      };
    }
  }

  /**
   * 获取相对时间描述
   * @param {number} timestamp 时间戳
   * @returns {string} 相对时间
   */
  getRelativeTime(timestamp) {
    try {
      const now = Date.now();
      const diff = now - timestamp;

      if (diff < 1000) return '刚刚';
      if (diff < 60000) return `${Math.floor(diff / 1000)}秒前`;
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;

      return `${Math.floor(diff / 86400000)}天前`;
    } catch (error) {
      return '刚刚';
    }
  }

  /**
   * 获取页面上下文信息
   * @returns {Object} 页面上下文
   */
  getPageContext() {
    try {
      const context = {
        title: document.title,
        url: this.getSimplifiedUrl(window.location.href),
        domain: window.location.hostname,
        path: window.location.pathname,
        viewport: `${window.innerWidth}x${window.innerHeight}`,
        scrollPosition: Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100) || 0
      };

      console.log('🌐 页面上下文:', context);
      return context;
    } catch (error) {
      console.error('❌ 获取页面上下文失败:', error);
      return {
        title: '未知页面',
        url: '未知',
        domain: '未知',
        path: '/',
        viewport: '未知',
        scrollPosition: 0
      };
    }
  }

  /**
   * 简化URL显示
   * @param {string} url 完整URL
   * @returns {string} 简化的URL
   */
  getSimplifiedUrl(url) {
    try {
      const urlObj = new URL(url);
      // 只保留域名和路径的关键部分
      let simplified = urlObj.hostname;
      if (urlObj.pathname !== '/') {
        const pathParts = urlObj.pathname.split('/').filter(part => part);
        if (pathParts.length > 0) {
          simplified += '/' + pathParts.slice(0, 2).join('/'); // 只取前两级路径
          if (pathParts.length > 2) simplified += '/...';
        }
      }
      return simplified;
    } catch (error) {
      return url.substring(0, 50) + (url.length > 50 ? '...' : '');
    }
  }

  /**
   * 获取元素路径信息
   * @param {Object} targetInfo 目标元素信息
   * @returns {Object} 路径信息
   */
  getElementPath(targetInfo) {
    if (!targetInfo) return null;

    try {
      const element = this.findTargetElement(targetInfo);
      if (!element) {
        return {
          selector: '元素未找到',
          hierarchy: '未知',
          context: '无法获取'
        };
      }

      const pathInfo = {
        selector: this.generateSelector(element),
        hierarchy: this.getElementHierarchy(element),
        context: this.getElementContext(element),
        position: this.getElementPosition(element)
      };

      console.log('🗺️ 元素路径信息:', pathInfo);
      return pathInfo;
    } catch (error) {
      console.error('❌ 获取元素路径失败:', error);
      return null;
    }
  }

  /**
   * 生成元素选择器
   * @param {Element} element 元素
   * @returns {string} CSS选择器
   */
  generateSelector(element) {
    try {
      // 优先使用ID
      if (element.id) {
        return `#${element.id}`;
      }

      // 使用类名（取前2个有意义的类）
      if (element.className) {
        const classes = element.className.split(' ')
          .filter(cls => cls && !cls.match(/^(css-|_)/)) // 过滤掉CSS-in-JS生成的类名
          .slice(0, 2);
        if (classes.length > 0) {
          return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
        }
      }

      // 使用标签名和位置
      const parent = element.parentElement;
      if (parent) {
        const siblings = Array.from(parent.children).filter(child => child.tagName === element.tagName);
        if (siblings.length > 1) {
          const index = siblings.indexOf(element) + 1;
          return `${element.tagName.toLowerCase()}:nth-of-type(${index})`;
        }
      }

      return element.tagName.toLowerCase();
    } catch (error) {
      return '未知选择器';
    }
  }

  /**
   * 获取元素层级结构
   * @param {Element} element 元素
   * @returns {string} 层级结构
   */
  getElementHierarchy(element) {
    try {
      const hierarchy = [];
      let current = element;
      let depth = 0;

      while (current && current !== document.body && depth < 4) {
        let nodeDesc = current.tagName.toLowerCase();

        // 添加有意义的标识符
        if (current.id) {
          nodeDesc += `#${current.id}`;
        } else if (current.className) {
          const mainClass = current.className.split(' ')[0];
          if (mainClass && !mainClass.match(/^(css-|_)/)) {
            nodeDesc += `.${mainClass}`;
          }
        }

        hierarchy.unshift(nodeDesc);
        current = current.parentElement;
        depth++;
      }

      return hierarchy.join(' > ');
    } catch (error) {
      return '未知层级';
    }
  }

  /**
   * 获取元素上下文
   * @param {Element} element 元素
   * @returns {string} 上下文描述
   */
  getElementContext(element) {
    try {
      const context = [];

      // 检查是否在表单中
      const form = element.closest('form');
      if (form) {
        context.push('表单内');
      }

      // 检查是否在模态框中
      const modal = element.closest('[role="dialog"], .modal, .popup');
      if (modal) {
        context.push('弹窗内');
      }

      // 检查是否在导航中
      const nav = element.closest('nav, [role="navigation"]');
      if (nav) {
        context.push('导航栏');
      }

      // 检查是否在侧边栏中
      const sidebar = element.closest('.sidebar, .side-panel, aside');
      if (sidebar) {
        context.push('侧边栏');
      }

      // 检查是否在头部
      const header = element.closest('header, .header');
      if (header) {
        context.push('页头');
      }

      // 检查是否在主内容区
      const main = element.closest('main, .main-content, .content');
      if (main) {
        context.push('主内容区');
      }

      return context.length > 0 ? context.join(', ') : '页面主体';
    } catch (error) {
      return '未知区域';
    }
  }

  /**
   * 获取元素位置信息
   * @param {Element} element 元素
   * @returns {string} 位置描述
   */
  getElementPosition(element) {
    try {
      const rect = element.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;

      let position = '';

      // 垂直位置
      if (rect.top < viewportHeight * 0.33) {
        position += '上部';
      } else if (rect.top < viewportHeight * 0.67) {
        position += '中部';
      } else {
        position += '下部';
      }

      // 水平位置
      if (rect.left < viewportWidth * 0.33) {
        position += '左侧';
      } else if (rect.left < viewportWidth * 0.67) {
        position += '中央';
      } else {
        position += '右侧';
      }

      return position;
    } catch (error) {
      return '未知位置';
    }
  }

  /**
   * 构建增强的反馈消息
   * @param {string} type 操作类型
   * @param {string} description 操作描述
   * @param {Object} elementInfo 元素信息
   * @param {string} value 输入值
   * @param {Object} pageContext 页面上下文
   * @param {Object} pathInfo 路径信息
   * @param {Object} timeInfo 时间信息
   * @returns {string} 增强消息
   */
  buildEnhancedMessage(type, description, elementInfo, value, pageContext, pathInfo, timeInfo) {
    let message = `🎯 用户${this.getActionVerb(type)}`;

    // 添加元素信息
    if (elementInfo) {
      if (elementInfo.role) {
        message += `${elementInfo.role}${elementInfo.type}`;
      } else if (elementInfo.text) {
        message += `"${elementInfo.text}"${elementInfo.type}`;
      } else {
        message += elementInfo.type;
      }
    } else {
      message += description;
    }

    // 添加输入值信息
    if (value && type === 'input') {
      const valueType = this.getValueType(value);
      message += `，输入${valueType}: "${value.length > 50 ? value.substring(0, 50) + '...' : value}"`;
    }

    // 添加时间信息
    if (timeInfo) {
      message += `\n⏰ 时间: ${timeInfo.time}`;
      if (timeInfo.relative !== '刚刚') {
        message += ` (${timeInfo.relative})`;
      }
    }

    // 添加位置和上下文信息
    if (pathInfo) {
      message += `\n📍 位置: ${pathInfo.context}${pathInfo.position ? ` (${pathInfo.position})` : ''}`;

      if (pathInfo.hierarchy && pathInfo.hierarchy !== '未知层级') {
        message += `\n🗂️ 路径: ${pathInfo.hierarchy}`;
      }

      if (pathInfo.selector && pathInfo.selector !== '未知选择器' && pathInfo.selector !== elementInfo?.tag) {
        message += `\n🎯 选择器: ${pathInfo.selector}`;
      }
    }

    // 添加页面上下文
    if (pageContext) {
      message += `\n🌐 页面: ${pageContext.title} (${pageContext.url})`;

      if (pageContext.scrollPosition > 0) {
        message += ` | 滚动: ${pageContext.scrollPosition}%`;
      }
    }

    return message;
  }

  /**
   * 构建核心反馈消息（保留兼容性）
   * @param {string} type 操作类型
   * @param {string} description 操作描述
   * @param {Object} elementInfo 元素信息
   * @param {string} value 输入值
   * @returns {string} 核心消息
   */
  buildCoreMessage(type, description, elementInfo, value) {
    let message = `用户${this.getActionVerb(type)}`;

    // 添加元素信息
    if (elementInfo) {
      if (elementInfo.role) {
        message += `${elementInfo.role}${elementInfo.type}`;
      } else if (elementInfo.text) {
        message += `"${elementInfo.text}"${elementInfo.type}`;
      } else {
        message += elementInfo.type;
      }
    } else {
      message += description;
    }

    // 添加输入值信息
    if (value && type === 'input') {
      const valueType = this.getValueType(value);
      message += `，输入${valueType}: "${value.length > 50 ? value.substring(0, 50) + '...' : value}"`;
    }

    return message;
  }

  /**
   * 获取操作动词
   * @param {string} type 操作类型
   * @returns {string} 动词
   */
  getActionVerb(type) {
    const verbMap = {
      'click': '点击了',
      'input': '在',
      'submit': '提交了',
      'hover': '悬停在',
      'scroll': '滚动了',
      'focus': '聚焦到',
      'blur': '离开了'
    };
    return verbMap[type] || '操作了';
  }

  /**
   * 获取输入值类型
   * @param {string} value 输入值
   * @returns {string} 值类型
   */
  getValueType(value) {
    if (!value) return '空内容';
    if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) return '邮箱';
    if (/^[\d\s\-\+\(\)]+$/.test(value) && value.replace(/\D/g, '').length >= 10) return '电话';
    if (/^https?:\/\//.test(value)) return '网址';
    if (/^\d+$/.test(value)) return '数字';
    if (/[\u4e00-\u9fa5]/.test(value)) return '中文';
    if (value.length > 100) return '长文本';
    return '文本';
  }


  


  /**
   * 获取当前状态
   */
  getStatus() {
    return { ...this.currentStatus };
  }
  


  /**
   * 检查是否已初始化
   */
  isReady() {
    return this.isInitialized;
  }



  /**
   * 销毁控制器
   */
  destroy() {
    console.log('🗑️ 主控制器正在销毁...');

    // 停止所有监控
    if (this.currentStatus.isMonitoring) {
      this.stopMonitoring();
    }

    // 清理资源
    this.isInitialized = false;

    console.log('✅ 主控制器已销毁');
  }
}

// 创建主控制器实例
const mainController = new MainController();

// 导出主控制器
window.MainController = mainController;

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
  if (window.MainController) {
    window.MainController.destroy();
  }
});

console.log('🎮 主控制器模块已加载');

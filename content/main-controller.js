/**
 * 主控制器 - 统一管理content script的各个模块
 * 负责协调各个模块之间的交互，处理来自popup和background的消息
 */

class MainController {
  constructor() {
    this.isInitialized = false;
    this.currentStatus = {
      isSupported: false,
      siteName: null,
      isNewChat: false,
      isMonitoring: false,
      autoRun: false,
      autoInject: false
    };
    
    this.init();
  }
  
  /**
   * 初始化主控制器
   */
  async init() {
    console.log('🎮 主控制器初始化中...');
    
    // 等待其他模块加载完成
    await this.waitForModules();
    
    // 设置消息监听器
    this.setupMessageHandlers();
    
    // 初始化状态
    await this.initializeStatus();
    
    this.isInitialized = true;
    console.log('✅ 主控制器初始化完成');
  }
  
  /**
   * 等待其他模块加载完成
   */
  async waitForModules() {
    const maxWaitTime = 5000; // 最大等待5秒
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      if (window.SiteConfigs && 
          window.MessageBridge && 
          window.LLMDetector && 
          window.BehaviorTracker && 
          window.AutoInjector && 
          window.CodeMonitor) {
        console.log('📦 所有模块已加载完成');
        return;
      }
      
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.warn('⚠️ 部分模块可能未加载完成');
  }
  
  /**
   * 设置消息处理器
   */
  setupMessageHandlers() {
    // 监听来自popup和background的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // 保持消息通道开放
    });
    
    // 监听内部模块消息
    window.MessageBridge.addListener(window.MESSAGE_TYPES.STATUS_UPDATE, (data) => {
      this.updateStatus(data);
    });
    
    console.log('📨 消息处理器已设置');
  }
  
  /**
   * 处理外部消息
   */
  async handleMessage(message, sender, sendResponse) {
    console.log('📨 主控制器收到消息:', message.type);
    
    try {
      switch (message.type) {
        case 'GET_STATUS':
          sendResponse(this.currentStatus);
          break;
          
        case 'INJECT_PROMPT':
          await this.handleInjectPrompt(message.data);
          sendResponse({ success: true });
          break;
          
        case 'START_MONITORING':
          await this.startMonitoring();
          sendResponse({ success: true });
          break;
          
        case 'STOP_MONITORING':
          await this.stopMonitoring();
          sendResponse({ success: true });
          break;
          
        case 'SETTING_UPDATED':
          await this.handleSettingUpdate(message.data);
          sendResponse({ success: true });
          break;
          
        case 'AUTO_RUN_CODE':
          await this.handleAutoRunCode(message.data);
          sendResponse({ success: true });
          break;

        case 'BEHAVIOR_DETECTED':
          await this.handleUserBehavior(message.data);
          sendResponse({ success: true });
          break;

        default:
          console.warn('⚠️ 未知消息类型:', message.type);
          sendResponse({ success: false, error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('❌ 处理消息时出错:', error);
      sendResponse({ success: false, error: error.message });
    }
  }
  
  /**
   * 初始化状态
   */
  async initializeStatus() {
    const config = window.SiteConfigs.getCurrentSiteConfig();

    this.currentStatus = {
      isSupported: !!config,
      siteName: config ? config.name : null,
      isNewChat: config ? window.SiteConfigs.isNewChatPage() : false,
      isMonitoring: true,   // 默认启用监控
      autoRun: true,        // 默认启用自动运行
      autoInject: true,     // 默认启用自动注入
      url: window.location.href
    };

    // 默认启用所有功能
    if (window.CodeMonitor) {
      window.CodeMonitor.enableAutoRun();
      console.log('🤖 默认启用自动运行功能');
    }

    if (window.AutoInjector) {
      window.AutoInjector.enableAutoMode();
      console.log('💉 默认启用自动注入功能');
    }

    if (window.BehaviorTracker) {
      window.BehaviorTracker.startTracking();
      console.log('👁️ 默认启用行为追踪功能');
    }

    // 发送初始状态到background
    window.MessageBridge.sendMessage(window.MESSAGE_TYPES.STATUS_UPDATE, this.currentStatus);
  }
  
  /**
   * 更新状态
   */
  updateStatus(newStatus) {
    this.currentStatus = { ...this.currentStatus, ...newStatus };
    console.log('📊 状态已更新:', this.currentStatus);
  }
  
  /**
   * 处理注入提示词
   */
  async handleInjectPrompt(data) {
    const { prompt, autoSend = false } = data;

    console.log('💉 开始注入提示词');

    try {
      // 检查自动注入器是否可用
      if (!window.AutoInjector) {
        throw new Error('自动注入器未初始化');
      }

      // 使用自动注入器注入提示词
      await window.AutoInjector.injectPrompt(prompt, autoSend);

      // 如果自动发送，启用相关功能
      if (autoSend) {
        // 启用自动运行和自动注入
        if (window.CodeMonitor) {
          window.CodeMonitor.enableAutoRun();
        }
        window.AutoInjector.enableAutoMode();

        // 启动行为追踪
        await this.startMonitoring();

        this.currentStatus.autoRun = true;
        this.currentStatus.autoInject = true;
      }

      console.log('✅ 提示词注入处理完成');

    } catch (error) {
      console.error('❌ 处理注入提示词时发生错误:', error);

      // 通知注入失败
      if (window.AutoInjector && window.AutoInjector.notifyInjectionFailed) {
        window.AutoInjector.notifyInjectionFailed(error.message);
      } else {
        // 如果自动注入器不可用，直接显示错误
        this.showErrorNotification(`注入失败: ${error.message}`);
      }

      throw error; // 重新抛出错误，让调用者知道失败了
    }
  }

  /**
   * 显示错误通知
   * @param {string} message 错误消息
   */
  showErrorNotification(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #f44336;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      z-index: 10000;
      font-family: Arial, sans-serif;
      font-size: 14px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      max-width: 300px;
      word-wrap: break-word;
    `;

    notification.textContent = message;
    document.body.appendChild(notification);

    // 5秒后自动移除
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }
  
  /**
   * 开始监控
   */
  async startMonitoring() {
    console.log('▶️ 开始监控用户行为');

    // 启用行为追踪
    window.BehaviorTracker.startTracking();

    // 启用代码监控和自动运行
    window.CodeMonitor.enableAutoRun();

    // 启用自动注入
    window.AutoInjector.enableAutoMode();

    this.currentStatus.isMonitoring = true;
    this.currentStatus.autoRun = true;
    this.currentStatus.autoInject = true;

    // 发送状态更新
    window.MessageBridge.sendMessage(window.MESSAGE_TYPES.STATUS_UPDATE, {
      isMonitoring: true,
      autoRun: true,
      message: '用户行为监控和自动运行已启动'
    });

    console.log('✅ 监控已启动，自动运行已启用');
  }
  
  /**
   * 停止监控
   */
  async stopMonitoring() {
    console.log('⏹️ 停止监控用户行为');
    
    // 停止行为追踪
    window.BehaviorTracker.stopTracking();
    
    // 禁用自动注入
    window.AutoInjector.disableAutoMode();
    
    this.currentStatus.isMonitoring = false;
    this.currentStatus.autoInject = false;
    
    // 发送状态更新
    window.MessageBridge.sendMessage(window.MESSAGE_TYPES.STATUS_UPDATE, {
      isMonitoring: false,
      message: '用户行为监控已停止'
    });
  }
  
  /**
   * 处理设置更新
   */
  async handleSettingUpdate(settings) {
    console.log('⚙️ 更新设置:', settings);
    
    Object.keys(settings).forEach(key => {
      const value = settings[key];
      
      switch (key) {
        case 'autoRun':
          if (value) {
            window.CodeMonitor.enableAutoRun();
          } else {
            window.CodeMonitor.disableAutoRun();
          }
          this.currentStatus.autoRun = value;
          break;
          
        case 'autoInject':
          if (value) {
            window.AutoInjector.enableAutoMode();
          } else {
            window.AutoInjector.disableAutoMode();
          }
          this.currentStatus.autoInject = value;
          break;
      }
    });
  }
  
  /**
   * 处理自动运行代码
   */
  async handleAutoRunCode(data) {
    console.log('🤖 处理自动运行代码请求');

    // 这里可以添加特定的自动运行逻辑
    // 目前由CodeMonitor自动处理
  }

  /**
   * 处理用户行为
   * @param {Object} behaviorData 行为数据
   */
  async handleUserBehavior(behaviorData) {
    console.log('👤 处理用户行为:', behaviorData.description);

    // 检查是否启用了自动注入模式
    if (!this.currentStatus.autoInject) {
      console.log('⏭️ 自动注入模式未启用，跳过行为处理');
      return;
    }

    try {
      // 将用户行为转换为自然语言反馈
      const feedbackMessage = this.generateBehaviorFeedback(behaviorData);

      // 自动发送反馈给LLM
      if (window.AutoInjector) {
        await window.AutoInjector.injectPrompt(feedbackMessage, true);
        console.log('✅ 用户行为反馈已发送给LLM');
      }

    } catch (error) {
      console.error('❌ 处理用户行为时出错:', error);
    }
  }

  /**
   * 生成行为反馈消息
   * @param {Object} behaviorData 行为数据
   * @returns {string} 反馈消息
   */
  generateBehaviorFeedback(behaviorData) {
    const { type, description, target, value, timestamp, coordinates } = behaviorData;

    // 获取时间信息
    const now = new Date(timestamp);
    const timeStr = now.toLocaleTimeString('zh-CN', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });

    // 收集页面上下文信息
    const pageContext = this.collectPageContext();

    // 收集元素详细信息
    const elementDetails = this.collectElementDetails(target);

    // 收集用户选择信息
    const selectionInfo = this.collectSelectionInfo();

    // 构建详细的反馈消息
    let feedback = `=== 用户交互反馈 ===\n`;
    feedback += `时间: ${timeStr}\n`;
    feedback += `操作类型: ${type}\n`;
    feedback += `操作描述: ${description}\n\n`;

    // 添加元素详细信息
    if (elementDetails) {
      feedback += `=== 操作目标元素 ===\n`;
      feedback += `标签: ${elementDetails.tagName}\n`;
      feedback += `文本内容: "${elementDetails.text}"\n`;
      feedback += `CSS类: ${elementDetails.className}\n`;
      feedback += `ID: ${elementDetails.id || '无'}\n`;
      feedback += `位置: ${elementDetails.position}\n`;
      if (elementDetails.attributes) {
        feedback += `属性: ${elementDetails.attributes}\n`;
      }
      feedback += `\n`;
    }

    // 添加坐标信息
    if (coordinates) {
      feedback += `点击坐标: (${coordinates.x}, ${coordinates.y})\n\n`;
    }

    // 根据操作类型添加特定信息
    switch (type) {
      case 'click':
        feedback += `=== 点击操作详情 ===\n`;
        feedback += this.generateClickDetails(behaviorData, elementDetails);
        break;

      case 'input':
        feedback += `=== 输入操作详情 ===\n`;
        feedback += this.generateInputDetails(behaviorData, elementDetails);
        break;

      case 'submit':
        feedback += `=== 表单提交详情 ===\n`;
        feedback += this.generateSubmitDetails(behaviorData, elementDetails);
        break;

      case 'hover':
        feedback += `=== 悬停操作详情 ===\n`;
        feedback += this.generateHoverDetails(behaviorData, elementDetails);
        break;

      case 'scroll':
        feedback += `=== 滚动操作详情 ===\n`;
        feedback += this.generateScrollDetails(behaviorData);
        break;
    }

    // 添加用户选择信息
    if (selectionInfo.hasSelection) {
      feedback += `\n=== 用户选择内容 ===\n`;
      feedback += `选中文本: "${selectionInfo.selectedText}"\n`;
      feedback += `选择范围: ${selectionInfo.selectionRange}\n`;
    }

    // 添加页面上下文
    feedback += `\n=== 页面上下文 ===\n`;
    feedback += `页面标题: ${pageContext.title}\n`;
    feedback += `页面URL: ${pageContext.url}\n`;
    feedback += `视口大小: ${pageContext.viewport.width}x${pageContext.viewport.height}\n`;
    feedback += `滚动位置: (${pageContext.scroll.x}, ${pageContext.scroll.y})\n`;
    feedback += `页面元素总数: ${pageContext.elementCount}\n`;

    // 添加周围元素信息
    const surroundingElements = this.collectSurroundingElements(target);
    if (surroundingElements.length > 0) {
      feedback += `\n=== 周围元素 ===\n`;
      surroundingElements.forEach((elem, index) => {
        feedback += `${index + 1}. ${elem.tagName}: "${elem.text}" (${elem.className})\n`;
      });
    }

    // 添加操作建议
    feedback += `\n=== 操作建议 ===\n`;
    feedback += this.generateActionSuggestions(type, behaviorData, elementDetails);

    feedback += `\n请根据以上详细的用户操作信息，提供准确、相关的界面更新和反馈。保持界面的连续性和用户体验的流畅性。`;

    return feedback;
  }
  
  /**
   * 收集页面上下文信息
   * @returns {Object} 页面上下文
   */
  collectPageContext() {
    return {
      title: document.title,
      url: window.location.href,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      scroll: {
        x: window.scrollX,
        y: window.scrollY
      },
      elementCount: document.querySelectorAll('*').length,
      timestamp: Date.now()
    };
  }

  /**
   * 收集元素详细信息
   * @param {Object} targetInfo 目标元素信息
   * @returns {Object} 元素详细信息
   */
  collectElementDetails(targetInfo) {
    if (!targetInfo) return null;

    try {
      // 尝试通过多种方式找到实际的DOM元素
      let element = null;

      if (targetInfo.id) {
        element = document.getElementById(targetInfo.id);
      }

      if (!element && targetInfo.className) {
        const elements = document.getElementsByClassName(targetInfo.className);
        if (elements.length > 0) {
          // 通过文本内容匹配找到正确的元素
          for (let elem of elements) {
            if (elem.textContent.includes(targetInfo.text.substring(0, 20))) {
              element = elem;
              break;
            }
          }
          if (!element) element = elements[0];
        }
      }

      if (!element) {
        // 通过标签名和文本内容查找
        const elements = document.querySelectorAll(targetInfo.tagName);
        for (let elem of elements) {
          if (elem.textContent.includes(targetInfo.text.substring(0, 20))) {
            element = elem;
            break;
          }
        }
      }

      if (!element) {
        return {
          tagName: targetInfo.tagName || '未知',
          text: targetInfo.text || '',
          className: targetInfo.className || '',
          id: targetInfo.id || '',
          position: '元素未找到',
          attributes: '无法获取'
        };
      }

      // 获取元素位置信息
      const rect = element.getBoundingClientRect();
      const position = `(${Math.round(rect.left)}, ${Math.round(rect.top)}) 大小: ${Math.round(rect.width)}x${Math.round(rect.height)}`;

      // 收集重要属性
      const attributes = [];
      if (element.type) attributes.push(`type="${element.type}"`);
      if (element.name) attributes.push(`name="${element.name}"`);
      if (element.value) attributes.push(`value="${element.value.substring(0, 50)}"`);
      if (element.placeholder) attributes.push(`placeholder="${element.placeholder}"`);
      if (element.href) attributes.push(`href="${element.href}"`);
      if (element.src) attributes.push(`src="${element.src}"`);

      return {
        tagName: element.tagName,
        text: element.textContent.trim().substring(0, 100),
        className: element.className,
        id: element.id,
        position: position,
        attributes: attributes.join(', ') || '无特殊属性',
        computedStyle: this.getRelevantStyles(element)
      };

    } catch (error) {
      console.error('❌ 收集元素详细信息时出错:', error);
      return null;
    }
  }

  /**
   * 获取相关的CSS样式
   * @param {Element} element 元素
   * @returns {string} 样式信息
   */
  getRelevantStyles(element) {
    try {
      const styles = window.getComputedStyle(element);
      const relevantStyles = [];

      if (styles.display !== 'block') relevantStyles.push(`display: ${styles.display}`);
      if (styles.position !== 'static') relevantStyles.push(`position: ${styles.position}`);
      if (styles.backgroundColor !== 'rgba(0, 0, 0, 0)') relevantStyles.push(`background: ${styles.backgroundColor}`);
      if (styles.color !== 'rgb(0, 0, 0)') relevantStyles.push(`color: ${styles.color}`);
      if (styles.fontSize !== '16px') relevantStyles.push(`font-size: ${styles.fontSize}`);

      return relevantStyles.join('; ') || '默认样式';
    } catch (error) {
      return '样式获取失败';
    }
  }

  /**
   * 收集用户选择信息
   * @returns {Object} 选择信息
   */
  collectSelectionInfo() {
    try {
      const selection = window.getSelection();

      if (selection.rangeCount === 0) {
        return { hasSelection: false };
      }

      const selectedText = selection.toString();
      if (!selectedText.trim()) {
        return { hasSelection: false };
      }

      const range = selection.getRangeAt(0);
      const startContainer = range.startContainer;
      const endContainer = range.endContainer;

      return {
        hasSelection: true,
        selectedText: selectedText.substring(0, 200), // 限制长度
        selectionRange: `从第${range.startOffset}字符到第${range.endOffset}字符`,
        startElement: startContainer.nodeType === Node.TEXT_NODE ?
                     startContainer.parentElement.tagName : startContainer.tagName,
        endElement: endContainer.nodeType === Node.TEXT_NODE ?
                   endContainer.parentElement.tagName : endContainer.tagName
      };

    } catch (error) {
      console.error('❌ 收集选择信息时出错:', error);
      return { hasSelection: false };
    }
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return { ...this.currentStatus };
  }
  
  /**
   * 收集周围元素信息
   * @param {Object} targetInfo 目标元素信息
   * @returns {Array} 周围元素信息
   */
  collectSurroundingElements(targetInfo) {
    try {
      if (!targetInfo) return [];

      // 尝试找到目标元素
      let targetElement = null;
      if (targetInfo.id) {
        targetElement = document.getElementById(targetInfo.id);
      }

      if (!targetElement && targetInfo.className) {
        const elements = document.getElementsByClassName(targetInfo.className);
        for (let elem of elements) {
          if (elem.textContent.includes(targetInfo.text.substring(0, 20))) {
            targetElement = elem;
            break;
          }
        }
      }

      if (!targetElement) return [];

      const surroundingElements = [];

      // 获取父元素
      if (targetElement.parentElement) {
        surroundingElements.push({
          relation: '父元素',
          tagName: targetElement.parentElement.tagName,
          text: targetElement.parentElement.textContent.trim().substring(0, 50),
          className: targetElement.parentElement.className
        });
      }

      // 获取兄弟元素
      const siblings = Array.from(targetElement.parentElement?.children || []);
      siblings.forEach(sibling => {
        if (sibling !== targetElement && sibling.textContent.trim()) {
          surroundingElements.push({
            relation: '兄弟元素',
            tagName: sibling.tagName,
            text: sibling.textContent.trim().substring(0, 50),
            className: sibling.className
          });
        }
      });

      // 获取子元素
      const children = Array.from(targetElement.children);
      children.slice(0, 3).forEach(child => { // 只取前3个子元素
        if (child.textContent.trim()) {
          surroundingElements.push({
            relation: '子元素',
            tagName: child.tagName,
            text: child.textContent.trim().substring(0, 50),
            className: child.className
          });
        }
      });

      return surroundingElements.slice(0, 10); // 限制数量

    } catch (error) {
      console.error('❌ 收集周围元素信息时出错:', error);
      return [];
    }
  }

  /**
   * 生成点击操作详情
   * @param {Object} behaviorData 行为数据
   * @param {Object} elementDetails 元素详情
   * @returns {string} 点击详情
   */
  generateClickDetails(behaviorData, elementDetails) {
    let details = '';

    if (elementDetails) {
      details += `点击的元素类型: ${elementDetails.tagName}\n`;

      // 根据元素类型提供特定信息
      switch (elementDetails.tagName.toLowerCase()) {
        case 'button':
          details += `按钮功能: ${elementDetails.text || '未知'}\n`;
          details += `按钮状态: ${elementDetails.attributes.includes('disabled') ? '禁用' : '可用'}\n`;
          break;

        case 'a':
          details += `链接文本: ${elementDetails.text}\n`;
          if (elementDetails.attributes.includes('href')) {
            details += `链接目标: ${elementDetails.attributes.match(/href="([^"]*)"/) ? elementDetails.attributes.match(/href="([^"]*)"/)[1] : '未知'}\n`;
          }
          break;

        case 'input':
          details += `输入框类型: ${elementDetails.attributes.includes('type') ? elementDetails.attributes.match(/type="([^"]*)"/) ? elementDetails.attributes.match(/type="([^"]*)"/)[1] : 'text' : 'text'}\n`;
          if (elementDetails.attributes.includes('value')) {
            details += `当前值: ${elementDetails.attributes.match(/value="([^"]*)"/) ? elementDetails.attributes.match(/value="([^"]*)"/)[1] : ''}\n`;
          }
          break;

        case 'div':
        case 'span':
          details += `容器内容: ${elementDetails.text}\n`;
          details += `可能的功能: ${this.inferElementFunction(elementDetails)}\n`;
          break;
      }
    }

    details += `用户意图分析: ${this.analyzeUserIntent('click', behaviorData, elementDetails)}\n`;
    details += `建议响应: ${this.suggestResponse('click', behaviorData, elementDetails)}\n`;

    return details;
  }

  /**
   * 生成输入操作详情
   * @param {Object} behaviorData 行为数据
   * @param {Object} elementDetails 元素详情
   * @returns {string} 输入详情
   */
  generateInputDetails(behaviorData, elementDetails) {
    const { value } = behaviorData;
    let details = '';

    details += `输入内容: "${value || ''}"\n`;
    details += `输入长度: ${(value || '').length} 字符\n`;

    if (elementDetails) {
      details += `输入框标签: ${elementDetails.tagName}\n`;
      if (elementDetails.attributes.includes('placeholder')) {
        details += `占位符: ${elementDetails.attributes.match(/placeholder="([^"]*)"/) ? elementDetails.attributes.match(/placeholder="([^"]*)"/)[1] : ''}\n`;
      }
      if (elementDetails.attributes.includes('name')) {
        details += `字段名称: ${elementDetails.attributes.match(/name="([^"]*)"/) ? elementDetails.attributes.match(/name="([^"]*)"/)[1] : ''}\n`;
      }
    }

    // 分析输入内容类型
    if (value) {
      details += `内容类型分析: ${this.analyzeInputType(value)}\n`;
      details += `输入模式: ${this.analyzeInputPattern(value)}\n`;
    }

    details += `用户意图分析: ${this.analyzeUserIntent('input', behaviorData, elementDetails)}\n`;
    details += `建议响应: ${this.suggestResponse('input', behaviorData, elementDetails)}\n`;

    return details;
  }

  /**
   * 检查是否已初始化
   */
  isReady() {
    return this.isInitialized;
  }
  
  /**
   * 生成表单提交详情
   * @param {Object} behaviorData 行为数据
   * @param {Object} elementDetails 元素详情
   * @returns {string} 提交详情
   */
  generateSubmitDetails(behaviorData, elementDetails) {
    const { formData } = behaviorData;
    let details = '';

    details += `表单提交时间: ${new Date(behaviorData.timestamp).toLocaleTimeString()}\n`;

    if (formData) {
      details += `提交的字段数量: ${Object.keys(formData).length}\n`;
      details += `表单数据:\n`;

      Object.entries(formData).forEach(([key, value]) => {
        // 对敏感信息进行脱敏处理
        let displayValue = value;
        if (key.toLowerCase().includes('password')) {
          displayValue = '****(已隐藏)';
        } else if (typeof value === 'string' && value.length > 100) {
          displayValue = value.substring(0, 100) + '...(截断)';
        }
        details += `  ${key}: ${displayValue}\n`;
      });
    }

    if (elementDetails) {
      details += `表单元素: ${elementDetails.tagName}\n`;
      details += `表单ID: ${elementDetails.id || '无'}\n`;
      details += `表单类: ${elementDetails.className}\n`;
    }

    details += `用户意图分析: ${this.analyzeUserIntent('submit', behaviorData, elementDetails)}\n`;
    details += `建议响应: ${this.suggestResponse('submit', behaviorData, elementDetails)}\n`;

    return details;
  }

  /**
   * 生成悬停操作详情
   * @param {Object} behaviorData 行为数据
   * @param {Object} elementDetails 元素详情
   * @returns {string} 悬停详情
   */
  generateHoverDetails(behaviorData, elementDetails) {
    let details = '';

    details += `悬停时间: ${new Date(behaviorData.timestamp).toLocaleTimeString()}\n`;

    if (elementDetails) {
      details += `悬停元素: ${elementDetails.tagName}\n`;
      details += `元素内容: ${elementDetails.text}\n`;
      details += `元素功能推测: ${this.inferElementFunction(elementDetails)}\n`;
    }

    details += `用户意图分析: 用户可能对此元素感兴趣，正在考虑是否进行操作\n`;
    details += `建议响应: 可以考虑显示相关提示信息或预览内容\n`;

    return details;
  }

  /**
   * 生成滚动操作详情
   * @param {Object} behaviorData 行为数据
   * @returns {string} 滚动详情
   */
  generateScrollDetails(behaviorData) {
    const { scrollPercentage } = behaviorData;
    let details = '';

    details += `滚动时间: ${new Date(behaviorData.timestamp).toLocaleTimeString()}\n`;
    details += `滚动位置: ${scrollPercentage}%\n`;

    // 分析滚动行为
    if (scrollPercentage < 25) {
      details += `滚动分析: 用户在页面顶部区域浏览\n`;
      details += `建议响应: 确保重要信息在顶部可见\n`;
    } else if (scrollPercentage < 75) {
      details += `滚动分析: 用户在页面中部区域浏览\n`;
      details += `建议响应: 保持内容的连贯性和可读性\n`;
    } else {
      details += `滚动分析: 用户已浏览到页面底部\n`;
      details += `建议响应: 可能需要提供更多内容或总结信息\n`;
    }

    return details;
  }

  /**
   * 推断元素功能
   * @param {Object} elementDetails 元素详情
   * @returns {string} 功能推测
   */
  inferElementFunction(elementDetails) {
    if (!elementDetails) return '未知功能';

    const { tagName, text, className, attributes } = elementDetails;

    // 基于标签名推断
    switch (tagName.toLowerCase()) {
      case 'button':
        if (text.includes('提交') || text.includes('submit')) return '表单提交按钮';
        if (text.includes('取消') || text.includes('cancel')) return '取消操作按钮';
        if (text.includes('确认') || text.includes('confirm')) return '确认操作按钮';
        if (text.includes('删除') || text.includes('delete')) return '删除操作按钮';
        return '操作按钮';

      case 'input':
        if (attributes.includes('type="email"')) return '邮箱输入框';
        if (attributes.includes('type="password"')) return '密码输入框';
        if (attributes.includes('type="search"')) return '搜索输入框';
        if (attributes.includes('type="tel"')) return '电话号码输入框';
        return '文本输入框';

      case 'a':
        if (attributes.includes('href')) return '导航链接';
        return '链接元素';

      case 'div':
        if (className.includes('card')) return '卡片容器';
        if (className.includes('modal')) return '弹窗容器';
        if (className.includes('menu')) return '菜单容器';
        if (className.includes('button')) return '按钮容器';
        return '内容容器';

      default:
        return `${tagName}元素`;
    }
  }

  /**
   * 分析用户意图
   * @param {string} actionType 操作类型
   * @param {Object} behaviorData 行为数据
   * @param {Object} elementDetails 元素详情
   * @returns {string} 意图分析
   */
  analyzeUserIntent(actionType, behaviorData, elementDetails) {
    switch (actionType) {
      case 'click':
        if (elementDetails?.tagName.toLowerCase() === 'button') {
          return `用户想要执行"${elementDetails.text}"操作`;
        }
        if (elementDetails?.tagName.toLowerCase() === 'a') {
          return '用户想要导航到新页面或查看更多信息';
        }
        return '用户想要与此元素进行交互';

      case 'input':
        return '用户正在输入信息，期望系统能够处理和响应输入内容';

      case 'submit':
        return '用户已完成信息填写，期望系统处理提交的数据';

      case 'hover':
        return '用户对此元素感兴趣，可能正在考虑下一步操作';

      case 'scroll':
        return '用户正在浏览页面内容，寻找相关信息';

      default:
        return '用户正在与界面进行交互';
    }
  }

  /**
   * 建议响应方式
   * @param {string} actionType 操作类型
   * @param {Object} behaviorData 行为数据
   * @param {Object} elementDetails 元素详情
   * @returns {string} 响应建议
   */
  suggestResponse(actionType, behaviorData, elementDetails) {
    switch (actionType) {
      case 'click':
        if (elementDetails?.text.includes('提交')) {
          return '处理表单数据，显示提交结果或跳转到结果页面';
        }
        if (elementDetails?.text.includes('搜索')) {
          return '执行搜索操作，显示搜索结果';
        }
        return '根据按钮功能执行相应操作，并提供视觉反馈';

      case 'input':
        return '实时验证输入内容，提供输入建议或错误提示';

      case 'submit':
        return '验证表单数据，处理业务逻辑，显示处理结果';

      case 'hover':
        return '显示工具提示、预览信息或高亮相关内容';

      case 'scroll':
        return '确保内容布局合理，必要时加载更多内容';

      default:
        return '提供适当的用户反馈和界面更新';
    }
  }

  /**
   * 分析输入内容类型
   * @param {string} value 输入值
   * @returns {string} 内容类型
   */
  analyzeInputType(value) {
    if (!value) return '空内容';

    // 邮箱格式
    if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) return '邮箱地址';

    // 电话号码格式
    if (/^[\d\s\-\+\(\)]+$/.test(value) && value.replace(/\D/g, '').length >= 10) return '电话号码';

    // 网址格式
    if (/^https?:\/\//.test(value)) return '网址链接';

    // 数字
    if (/^\d+$/.test(value)) return '纯数字';

    // 中文
    if (/[\u4e00-\u9fa5]/.test(value)) return '包含中文';

    // 英文
    if (/^[a-zA-Z\s]+$/.test(value)) return '英文文本';

    return '混合内容';
  }

  /**
   * 分析输入模式
   * @param {string} value 输入值
   * @returns {string} 输入模式
   */
  analyzeInputPattern(value) {
    if (!value) return '无输入';

    if (value.length < 5) return '短文本输入';
    if (value.length < 50) return '中等长度输入';
    if (value.length < 200) return '长文本输入';
    return '大量文本输入';
  }

  /**
   * 生成操作建议
   * @param {string} actionType 操作类型
   * @param {Object} behaviorData 行为数据
   * @param {Object} elementDetails 元素详情
   * @returns {string} 操作建议
   */
  generateActionSuggestions(actionType, behaviorData, elementDetails) {
    let suggestions = '';

    switch (actionType) {
      case 'click':
        suggestions += '1. 立即响应用户点击，提供视觉反馈\n';
        suggestions += '2. 根据点击的元素功能执行相应操作\n';
        suggestions += '3. 更新界面状态，显示操作结果\n';
        suggestions += '4. 如果是导航操作，准备页面跳转或内容切换\n';
        break;

      case 'input':
        suggestions += '1. 实时验证输入内容的有效性\n';
        suggestions += '2. 提供输入建议或自动完成功能\n';
        suggestions += '3. 显示字符计数或格式要求\n';
        suggestions += '4. 准备处理完整的输入内容\n';
        break;

      case 'submit':
        suggestions += '1. 验证所有必填字段\n';
        suggestions += '2. 显示提交进度或加载状态\n';
        suggestions += '3. 处理表单数据并保存\n';
        suggestions += '4. 显示成功消息或错误提示\n';
        break;

      case 'hover':
        suggestions += '1. 显示元素的详细信息或预览\n';
        suggestions += '2. 高亮相关的界面元素\n';
        suggestions += '3. 提供操作提示或帮助信息\n';
        break;

      case 'scroll':
        suggestions += '1. 确保重要内容在可视区域内\n';
        suggestions += '2. 考虑懒加载更多内容\n';
        suggestions += '3. 更新导航或进度指示器\n';
        break;

      default:
        suggestions += '1. 分析用户操作意图\n';
        suggestions += '2. 提供适当的界面反馈\n';
        suggestions += '3. 保持用户体验的连续性\n';
    }

    return suggestions;
  }

  /**
   * 销毁控制器
   */
  destroy() {
    console.log('🗑️ 主控制器正在销毁...');

    // 停止所有监控
    if (this.currentStatus.isMonitoring) {
      this.stopMonitoring();
    }

    // 清理资源
    this.isInitialized = false;

    console.log('✅ 主控制器已销毁');
  }
}

// 创建主控制器实例
const mainController = new MainController();

// 导出主控制器
window.MainController = mainController;

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
  if (window.MainController) {
    window.MainController.destroy();
  }
});

console.log('🎮 主控制器模块已加载');

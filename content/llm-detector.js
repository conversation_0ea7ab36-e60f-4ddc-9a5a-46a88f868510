/**
 * LLM网站检测器 - 检测当前网站类型和状态
 * 负责识别支持的LLM网站，并监控页面状态变化
 */

class LLMDetector {
  constructor() {
    this.currentSite = null;
    this.isMonitoring = false;
    this.pageObserver = null;
    
    this.init();
  }
  
  /**
   * 初始化检测器 - 优化版本，减少不必要的监听
   */
  init() {
    // console.log('🔍 LLM检测器初始化中...'); // 禁用日志

    // 检测当前网站
    this.detectCurrentSite();

    // 只在支持的网站上设置监听器
    if (this.currentSite) {
      // 设置轻量级页面变化监听
      this.setupLightweightObserver();

      // 监听URL变化（SPA应用）
      this.setupUrlChangeListener();
    }

    // 发送初始状态
    this.reportStatus();

    // console.log('✅ LLM检测器初始化完成'); // 禁用日志
  }
  
  /**
   * 检测当前网站类型 - 优化版本
   */
  detectCurrentSite() {
    const config = window.SiteConfigs?.getCurrentSiteConfig();

    if (config) {
      this.currentSite = config;
      // console.log('🎯 检测到支持的LLM网站:', config.name); // 禁用日志

      // 检查是否为新对话页面
      const isNewChat = window.SiteConfigs.isNewChatPage();
      if (isNewChat) {
        // console.log('🆕 当前为新对话页面，可以启动预见UI'); // 禁用日志
        this.notifyNewChatDetected();
      }

      return true;
    } else {
      // console.log('❌ 当前网站不在支持列表中'); // 禁用日志
      return false;
    }
  }
  
  /**
   * 设置轻量级观察器 - 大幅减少性能开销
   * 只在必要时进行检查，避免流式输出造成的性能问题
   */
  setupLightweightObserver() {
    // 使用节流的观察器，减少触发频率
    let observerTimeout = null;

    this.pageObserver = new MutationObserver((mutations) => {
      // 清除之前的定时器
      if (observerTimeout) {
        clearTimeout(observerTimeout);
      }

      // 节流处理，500ms内只处理一次
      observerTimeout = setTimeout(() => {
        // 只检查特定类型的变化，避免流式输出干扰
        const hasSignificantChanges = mutations.some(mutation => {
          return mutation.addedNodes.length > 0 &&
                 Array.from(mutation.addedNodes).some(node =>
                   node.nodeType === Node.ELEMENT_NODE &&
                   (node.tagName === 'PRE' ||
                    node.tagName === 'CODE' ||
                    node.className?.includes('code') ||
                    node.className?.includes('render'))
                 );
        });

        if (hasSignificantChanges) {
          this.checkForCodeBlocks(document.body);
        }
      }, 500);
    });

    // 只观察子节点变化，不观察属性和文本变化
    this.pageObserver.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: false,
      characterData: false
    });

    // console.log('👀 轻量级页面观察器已启动'); // 禁用日志
  }
  
  /**
   * 设置URL变化监听器 - 优化版本
   * 用于检测SPA应用的路由变化，减少不必要的处理
   */
  setupUrlChangeListener() {
    // 监听popstate事件（浏览器前进后退）
    window.addEventListener('popstate', () => {
      this.handleUrlChange();
    });

    // 监听pushState和replaceState（程序化导航）
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function(...args) {
      originalPushState.apply(history, args);
      setTimeout(() => this.handleUrlChange(), 200); // 增加延迟减少频率
    }.bind(this);

    history.replaceState = function(...args) {
      originalReplaceState.apply(history, args);
      setTimeout(() => this.handleUrlChange(), 200); // 增加延迟减少频率
    }.bind(this);

    // console.log('🔄 URL变化监听器已设置'); // 禁用日志
  }
  
  /**
   * 处理URL变化 - 优化版本
   */
  handleUrlChange() {
    // console.log('🔄 URL发生变化:', window.location.href); // 禁用日志

    // 重新检测网站状态
    this.detectCurrentSite();

    // 报告状态变化
    this.reportStatus();
  }
  
  /**
   * 检查代码块
   * @param {Element} element 要检查的元素
   */
  checkForCodeBlocks(element) {
    if (!this.currentSite) return;
    
    const codeBlocks = element.querySelectorAll(this.currentSite.codeBlockSelector);
    
    codeBlocks.forEach((codeBlock) => {
      // 检查是否已经处理过
      if (codeBlock.dataset.foreseeProcessed) return;
      
      // 标记为已处理
      codeBlock.dataset.foreseeProcessed = 'true';
      
      // 查找运行按钮
      const runButton = this.findRunButton(codeBlock);
      if (runButton) {
        // console.log('🏃 发现代码块运行按钮'); // 禁用日志
        this.setupRunButtonListener(runButton, codeBlock);

        // 通知发现代码块
        if (window.MessageBridge) {
          window.MessageBridge.sendMessage(window.MESSAGE_TYPES.CODE_BLOCK_FOUND, {
            codeBlock: this.getElementInfo(codeBlock),
            runButton: this.getElementInfo(runButton)
          });
        }
      }
    });
  }
  
  /**
   * 查找运行按钮
   * @param {Element} codeBlock 代码块元素
   * @returns {Element|null} 运行按钮元素
   */
  findRunButton(codeBlock) {
    // 在代码块的父容器中查找运行按钮
    let container = codeBlock.closest('.message, .chat-message, .response, .markdown-body') || codeBlock.parentElement;

    // 扩大搜索范围
    for (let i = 0; i < 5 && container; i++) {
      // 查找包含运行相关文本的按钮
      const buttons = container.querySelectorAll('button');
      for (let button of buttons) {
        const buttonText = button.textContent.trim().toLowerCase();
        const runKeywords = ['运行', 'run', '执行', 'execute', 'play', '▶'];

        if (runKeywords.some(keyword => buttonText.includes(keyword.toLowerCase()))) {
          return button;
        }

        // 检查按钮的aria-label或title
        const ariaLabel = button.getAttribute('aria-label') || '';
        const title = button.getAttribute('title') || '';
        if (runKeywords.some(keyword =>
          ariaLabel.toLowerCase().includes(keyword.toLowerCase()) ||
          title.toLowerCase().includes(keyword.toLowerCase())
        )) {
          return button;
        }
      }

      // 查找特定类名的按钮
      const runButton = container.querySelector('.run-button, .execute-button, .play-button');
      if (runButton) return runButton;

      container = container.parentElement;
    }

    return null;
  }
  
  /**
   * 设置运行按钮监听器 - 优化版本
   * @param {Element} runButton 运行按钮
   * @param {Element} codeBlock 代码块
   */
  setupRunButtonListener(runButton, codeBlock) {
    runButton.addEventListener('click', () => {
      // console.log('🏃 运行按钮被点击'); // 禁用日志

      if (window.MessageBridge) {
        window.MessageBridge.sendMessage(window.MESSAGE_TYPES.RUN_BUTTON_CLICKED, {
          codeBlock: this.getElementInfo(codeBlock),
          runButton: this.getElementInfo(runButton)
        });
      }

      // 延迟检查渲染结果，增加延迟减少频率
      setTimeout(() => {
        this.checkForRenderResults(document.body);
      }, 2000);
    });
  }
  
  /**
   * 检查渲染结果 - 优化版本
   * @param {Element} element 要检查的元素
   */
  checkForRenderResults(element) {
    if (!this.currentSite) return;

    const renderContainers = element.querySelectorAll(this.currentSite.renderContainerSelector);

    renderContainers.forEach((container) => {
      if (container.dataset.foreseeTracked) return;

      container.dataset.foreseeTracked = 'true';
      // console.log('🎨 发现渲染结果容器'); // 禁用日志

      // 启动行为追踪
      if (window.MessageBridge) {
        window.MessageBridge.sendMessage(window.MESSAGE_TYPES.RENDER_COMPLETED, {
          container: this.getElementInfo(container)
        });
      }
    });
  }
  
  /**
   * 通知检测到新对话 - 优化版本
   */
  notifyNewChatDetected() {
    if (window.MessageBridge) {
      window.MessageBridge.sendMessage(window.MESSAGE_TYPES.STATUS_UPDATE, {
        type: 'NEW_CHAT_DETECTED',
        site: this.currentSite.name,
        canStartForeseeUI: true
      });
    }
  }

  /**
   * 报告当前状态 - 优化版本
   */
  reportStatus() {
    const status = {
      site: this.currentSite ? this.currentSite.name : null,
      isSupported: !!this.currentSite,
      isNewChat: this.currentSite ? window.SiteConfigs.isNewChatPage() : false,
      url: window.location.href
    };

    if (window.MessageBridge) {
      window.MessageBridge.sendMessage(window.MESSAGE_TYPES.STATUS_UPDATE, status);
    }
    // console.log('📊 LLM检测器状态已更新:', status); // 禁用日志
  }
  
  /**
   * 获取元素信息
   * @param {Element} element 元素
   * @returns {Object} 元素信息
   */
  getElementInfo(element) {
    return {
      tagName: element.tagName,
      className: element.className,
      id: element.id,
      textContent: element.textContent.substring(0, 100) // 限制长度
    };
  }
  
  /**
   * 销毁检测器
   */
  destroy() {
    if (this.pageObserver) {
      this.pageObserver.disconnect();
    }
    console.log('🗑️ LLM检测器已销毁');
  }
}

// 创建检测器实例
const llmDetector = new LLMDetector();

// 导出检测器
window.LLMDetector = llmDetector;

console.log('🔍 LLM检测器模块已加载');

/**
 * 用户行为追踪器 - 监控用户在渲染页面上的交互行为
 * 将用户操作转换为自然语言描述，用于LLM理解用户意图
 */

class BehaviorTracker {
  constructor() {
    this.isTracking = false;
    this.trackedContainers = new Set();
    this.actionQueue = [];
    this.lastActionTime = 0;
    this.actionThrottleDelay = 500; // 防抖延迟
    this.contentObserver = null; // 内容观察器

    this.init();
  }
  
  /**
   * 初始化行为追踪器 - 优化版本，减少不必要的监听
   */
  init() {
    // console.log('👁️ 用户行为追踪器初始化中...'); // 禁用日志

    // 只在需要时监听事件
    if (window.MessageBridge) {
      // 监听渲染完成事件
      window.MessageBridge.addListener(window.MESSAGE_TYPES.RENDER_COMPLETED, (data) => {
        this.startTrackingContainer(data.container);
      });

      // 监听开始/停止追踪命令
      window.MessageBridge.addListener(window.MESSAGE_TYPES.START_MONITORING, () => {
        this.startTracking();
      });

      window.MessageBridge.addListener(window.MESSAGE_TYPES.STOP_MONITORING, () => {
        this.stopTracking();
      });
    }

    // console.log('✅ 用户行为追踪器初始化完成'); // 禁用日志
  }
  
  /**
   * 开始追踪指定容器 - 优化版本
   * @param {Object} containerInfo 容器信息
   */
  startTrackingContainer(containerInfo) {
    // 根据容器信息找到实际的DOM元素
    const container = this.findContainerElement(containerInfo);
    if (!container) {
      // console.warn('⚠️ 无法找到渲染容器元素'); // 禁用日志
      return;
    }

    if (this.trackedContainers.has(container)) {
      // console.log('ℹ️ 容器已在追踪中'); // 禁用日志
      return;
    }

    // console.log('🎯 开始追踪容器:', containerInfo); // 禁用日志
    this.trackedContainers.add(container);
    this.setupLightweightListeners(container); // 使用轻量级监听器
  }
  
  /**
   * 根据容器信息找到DOM元素
   * @param {Object} containerInfo 容器信息
   * @returns {Element|null} 找到的元素
   */
  findContainerElement(containerInfo) {
    // 尝试通过ID查找
    if (containerInfo.id) {
      const element = document.getElementById(containerInfo.id);
      if (element) return element;
    }
    
    // 尝试通过类名查找
    if (containerInfo.className) {
      const elements = document.getElementsByClassName(containerInfo.className);
      if (elements.length > 0) return elements[0];
    }
    
    // 尝试通过标签名和文本内容查找
    const elements = document.querySelectorAll(containerInfo.tagName);
    for (let element of elements) {
      if (element.textContent.includes(containerInfo.textContent.substring(0, 50))) {
        return element;
      }
    }
    
    return null;
  }
  
  /**
   * 为容器设置轻量级事件监听器 - 大幅减少性能开销
   * @param {Element} container 容器元素
   */
  setupLightweightListeners(container) {
    // 只监听关键的点击事件，使用节流
    let clickTimeout = null;
    container.addEventListener('click', (event) => {
      if (clickTimeout) return; // 防止频繁触发

      clickTimeout = setTimeout(() => {
        this.handleClickAction(event);
        clickTimeout = null;
      }, 200);
    }, { passive: true, capture: true });

    // 只监听重要的输入事件，大幅增加节流时间
    let inputTimeout = null;
    container.addEventListener('input', (event) => {
      if (inputTimeout) clearTimeout(inputTimeout);

      inputTimeout = setTimeout(() => {
        this.handleInputAction(event);
      }, 1000); // 增加到1秒
    }, { passive: true });

    // 移除悬停和滚动监听，这些会造成大量性能开销
    // 只保留表单提交监听
    container.addEventListener('submit', (event) => {
      this.handleSubmitAction(event);
    }, { passive: true });

    // console.log('👂 轻量级容器监听器已设置'); // 禁用日志
  }

  /**
   * 为容器设置事件监听器 - 保留原方法用于兼容性
   * @param {Element} container 容器元素
   */
  setupContainerListeners(container) {
    // 使用轻量级监听器
    this.setupLightweightListeners(container);
  }
  
  /**
   * 处理点击行为 - 优化版本
   * @param {Event} event 事件对象
   */
  handleClickAction(event) {
    const target = event.target;

    // 只处理重要的点击事件
    if (!this.isImportantElement(target)) {
      return;
    }

    const actionDescription = this.generateClickDescription(target);

    this.recordAction({
      type: 'click',
      target: this.getElementDescription(target),
      description: actionDescription,
      timestamp: Date.now(),
      coordinates: { x: event.clientX, y: event.clientY }
    });
  }
  
  /**
   * 处理输入行为 - 优化版本
   * @param {Event} event 事件对象
   */
  handleInputAction(event) {
    const target = event.target;
    const value = target.value;

    // 只处理有意义的输入
    if (!value || value.length < 2) {
      return;
    }

    const actionDescription = this.generateInputDescription(target, value);

    this.recordAction({
      type: 'input',
      target: this.getElementDescription(target),
      description: actionDescription,
      value: value.substring(0, 100), // 限制长度
      timestamp: Date.now()
    });
  }
  
  /**
   * 处理表单提交行为 - 优化版本
   * @param {Event} event 事件对象
   */
  handleSubmitAction(event) {
    const form = event.target;
    const formData = new FormData(form);
    const actionDescription = this.generateSubmitDescription(form, formData);

    this.recordAction({
      type: 'submit',
      target: this.getElementDescription(form),
      description: actionDescription,
      formData: Object.fromEntries(formData),
      timestamp: Date.now()
    });
  }
  
  // 悬停和滚动处理已移除，以提高性能
  
  /**
   * 生成点击行为描述
   * @param {Element} target 目标元素
   * @returns {string} 行为描述
   */
  generateClickDescription(target) {
    const elementType = this.getElementType(target);
    const elementText = this.getElementText(target);
    
    if (target.tagName === 'BUTTON') {
      return `用户点击了按钮"${elementText}"`;
    } else if (target.tagName === 'A') {
      return `用户点击了链接"${elementText}"`;
    } else if (target.tagName === 'INPUT') {
      if (target.type === 'checkbox') {
        return `用户${target.checked ? '选中' : '取消选中'}了复选框"${elementText}"`;
      } else if (target.type === 'radio') {
        return `用户选择了单选按钮"${elementText}"`;
      } else if (target.type === 'submit') {
        return `用户点击了提交按钮"${elementText}"`;
      }
    } else if (target.tagName === 'SELECT') {
      return `用户点击了下拉选择框"${elementText}"`;
    }
    
    return `用户点击了${elementType}"${elementText}"`;
  }
  
  /**
   * 生成输入行为描述
   * @param {Element} target 目标元素
   * @param {string} value 输入值
   * @returns {string} 行为描述
   */
  generateInputDescription(target, value) {
    const placeholder = target.placeholder || '';
    const label = this.findLabelForInput(target);
    
    if (target.type === 'password') {
      return `用户在密码输入框中输入了内容`;
    } else if (target.type === 'email') {
      return `用户在邮箱输入框中输入了: ${value}`;
    } else if (target.type === 'number') {
      return `用户在数字输入框中输入了: ${value}`;
    } else if (target.tagName === 'TEXTAREA') {
      return `用户在文本区域中输入了: ${value.substring(0, 50)}${value.length > 50 ? '...' : ''}`;
    }
    
    const fieldName = label || placeholder || '输入框';
    return `用户在"${fieldName}"中输入了: ${value}`;
  }
  
  /**
   * 生成提交行为描述
   * @param {Element} form 表单元素
   * @param {FormData} formData 表单数据
   * @returns {string} 行为描述
   */
  generateSubmitDescription(form, formData) {
    const formName = form.name || form.id || '表单';
    const fieldCount = formData.entries().length;
    
    return `用户提交了"${formName}"，包含${fieldCount}个字段`;
  }
  
  /**
   * 生成悬停行为描述
   * @param {Element} target 目标元素
   * @returns {string} 行为描述
   */
  generateHoverDescription(target) {
    const elementType = this.getElementType(target);
    const elementText = this.getElementText(target);
    
    return `用户将鼠标悬停在${elementType}"${elementText}"上`;
  }
  
  /**
   * 记录用户行为 - 优化版本
   * @param {Object} action 行为对象
   */
  recordAction(action) {
    // 防止重复记录相同的行为，增加间隔时间
    const now = Date.now();
    if (now - this.lastActionTime < 500) return; // 增加到500ms

    this.lastActionTime = now;
    this.actionQueue.push(action);

    // console.log('📝 记录用户行为:', action.description); // 禁用日志

    // 发送行为数据
    if (window.MessageBridge) {
      window.MessageBridge.sendMessage(window.MESSAGE_TYPES.BEHAVIOR_DETECTED, action);
    }

    // 限制队列长度
    if (this.actionQueue.length > 20) { // 减少队列长度
      this.actionQueue.shift();
    }
  }
  
  /**
   * 获取元素描述
   * @param {Element} element 元素
   * @returns {Object} 元素描述
   */
  getElementDescription(element) {
    return {
      tagName: element.tagName,
      id: element.id,
      className: element.className,
      text: this.getElementText(element)
    };
  }
  
  /**
   * 获取元素类型描述
   * @param {Element} element 元素
   * @returns {string} 类型描述
   */
  getElementType(element) {
    const tagName = element.tagName.toLowerCase();
    const typeMap = {
      'button': '按钮',
      'a': '链接',
      'input': '输入框',
      'textarea': '文本区域',
      'select': '下拉选择框',
      'div': '区域',
      'span': '文本',
      'img': '图片',
      'form': '表单'
    };
    
    return typeMap[tagName] || tagName;
  }
  
  /**
   * 获取元素文本内容
   * @param {Element} element 元素
   * @returns {string} 文本内容
   */
  getElementText(element) {
    let text = element.textContent || element.value || element.placeholder || element.alt || '';
    return text.trim().substring(0, 30);
  }
  
  /**
   * 查找输入框的标签
   * @param {Element} input 输入框元素
   * @returns {string} 标签文本
   */
  findLabelForInput(input) {
    // 通过for属性查找
    if (input.id) {
      const label = document.querySelector(`label[for="${input.id}"]`);
      if (label) return label.textContent.trim();
    }
    
    // 查找父级label
    const parentLabel = input.closest('label');
    if (parentLabel) return parentLabel.textContent.trim();
    
    return '';
  }
  
  /**
   * 判断是否为重要元素
   * @param {Element} element 元素
   * @returns {boolean} 是否重要
   */
  isImportantElement(element) {
    const importantTags = ['button', 'a', 'input', 'select', 'textarea'];
    return importantTags.includes(element.tagName.toLowerCase()) ||
           element.classList.contains('clickable') ||
           element.onclick !== null;
  }
  
  /**
   * 开始追踪 - 优化版本，减少全局监听
   */
  startTracking() {
    this.isTracking = true;
    // console.log('▶️ 开始用户行为追踪'); // 禁用日志

    // 只启动轻量级追踪，不进行全局监听
    this.startLightweightTracking();
  }

  /**
   * 启动轻量级追踪 - 大幅减少性能开销
   */
  startLightweightTracking() {
    // console.log('🌐 启动轻量级页面行为追踪'); // 禁用日志

    // 不追踪整个document，只追踪特定区域
    // this.setupContainerListeners(document); // 禁用全局监听

    // 只关注关键的LLM内容区域
    this.trackKeyLLMContent();
  }

  /**
   * 追踪关键LLM内容 - 轻量级版本
   */
  trackKeyLLMContent() {
    // 只查找最关键的容器，减少查询范围
    const keyContainers = [
      // 只关注按钮和输入框
      document.querySelectorAll('button'),
      document.querySelectorAll('input[type="text"], input[type="search"], textarea'),
      // 只关注可编辑内容
      document.querySelectorAll('[contenteditable="true"]')
    ];

    keyContainers.forEach(nodeList => {
      // 限制处理数量，避免性能问题
      const limitedList = Array.from(nodeList).slice(0, 10);
      limitedList.forEach(container => {
        if (!this.trackedContainers.has(container)) {
          // console.log('🎯 发现关键LLM内容容器，开始追踪'); // 禁用日志
          this.trackedContainers.add(container);
          this.setupLightweightListeners(container);
        }
      });
    });

    // 设置轻量级观察器
    this.setupLightweightContentObserver();
  }

  /**
   * 设置轻量级内容观察器 - 大幅减少性能开销
   */
  setupLightweightContentObserver() {
    if (this.contentObserver) {
      this.contentObserver.disconnect();
    }

    // 使用节流的观察器，大幅减少触发频率
    let observerTimeout = null;

    this.contentObserver = new MutationObserver((mutations) => {
      // 清除之前的定时器
      if (observerTimeout) {
        clearTimeout(observerTimeout);
      }

      // 大幅增加节流时间到2秒
      observerTimeout = setTimeout(() => {
        // 只检查关键的新增元素
        const hasKeyElements = mutations.some(mutation => {
          return Array.from(mutation.addedNodes).some(node =>
            node.nodeType === Node.ELEMENT_NODE &&
            (node.tagName === 'BUTTON' ||
             node.tagName === 'INPUT' ||
             node.tagName === 'TEXTAREA' ||
             node.contentEditable === 'true')
          );
        });

        if (hasKeyElements) {
          this.trackKeyLLMContent();
        }
      }, 2000);
    });

    // 只观察子节点变化，不观察属性和文本变化
    this.contentObserver.observe(document.body, {
      childList: true,
      subtree: false, // 不观察子树，减少开销
      attributes: false,
      characterData: false
    });
  }

  /**
   * 判断是否为潜在的LLM内容
   * @param {Element} element 元素
   * @returns {boolean} 是否为LLM内容
   */
  isPotentialLLMContent(element) {
    const className = element.className || '';
    const tagName = element.tagName.toLowerCase();

    // 检查类名
    const llmIndicators = ['message', 'chat', 'response', 'code', 'pre', 'output'];
    const hasLLMClass = llmIndicators.some(indicator =>
      className.toLowerCase().includes(indicator)
    );

    // 检查标签
    const llmTags = ['pre', 'code', 'iframe'];
    const isLLMTag = llmTags.includes(tagName);

    // 检查是否包含交互元素
    const hasInteractiveElements = element.querySelectorAll('button, input, a, [onclick]').length > 0;

    return hasLLMClass || isLLMTag || hasInteractiveElements;
  }
  
  /**
   * 停止追踪 - 优化版本
   */
  stopTracking() {
    this.isTracking = false;
    // console.log('⏹️ 停止用户行为追踪'); // 禁用日志

    // 清理观察器
    if (this.contentObserver) {
      this.contentObserver.disconnect();
      this.contentObserver = null;
    }

    // 清理所有追踪的容器
    this.trackedContainers.clear();
  }
  
  /**
   * 获取行为队列
   * @returns {Array} 行为队列
   */
  getActionQueue() {
    return [...this.actionQueue];
  }
  
  /**
   * 清空行为队列 - 优化版本
   */
  clearActionQueue() {
    this.actionQueue = [];
    // console.log('🗑️ 行为队列已清空'); // 禁用日志
  }
}

// 创建行为追踪器实例
const behaviorTracker = new BehaviorTracker();

// 导出追踪器
window.BehaviorTracker = behaviorTracker;

// console.log('👁️ 用户行为追踪器模块已加载'); // 禁用日志

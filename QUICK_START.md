# 🚀 预见UI插件快速开始指南

## 📋 项目概述

**预见UI**是一款革命性的Chrome插件，它让LLM生成的网页与用户实现真正的交互。通过监控用户在渲染页面上的操作行为，自动生成反馈并发送给LLM，实现持续的界面优化和功能完善。

### 🎯 核心价值
- **真正的交互**：不再是静态的代码展示，而是动态的应用体验
- **智能反馈**：用户的每个操作都会被理解并反馈给LLM
- **持续优化**：LLM根据用户行为不断改进界面
- **一键启动**：简单描述需求，即可生成完整应用

## ⚡ 5分钟快速体验

### 步骤1：安装插件（2分钟）
1. 下载项目文件到本地
2. 打开Chrome，访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"，选择项目文件夹
5. 看到插件图标出现在工具栏

### 步骤2：生成图标（1分钟）
1. 打开 `assets/icons/create-icons.html`
2. 点击"下载所有图标"按钮
3. 将下载的PNG文件放入 `assets/icons/` 目录
4. 刷新插件（在扩展程序页面点击刷新按钮）

### 步骤3：体验功能（2分钟）
1. 访问 https://chat.deepseek.com （或其他支持的LLM网站）
2. 点击"开启新对话"
3. 点击插件图标，输入："创建一个简单的计算器"
4. 点击"启动预见UI"
5. 等待LLM生成代码并自动运行
6. 在计算器上点击按钮，观察插件如何自动反馈给LLM

## 🎮 完整使用流程

### 场景1：创建待办事项应用

**输入描述**：
```
创建一个待办事项管理应用，包含添加任务、标记完成、删除任务的功能，界面要简洁美观
```

**选择模板**：工具模板

**预期效果**：
1. LLM生成完整的待办应用代码
2. 插件自动点击运行按钮
3. 应用在对话中渲染显示
4. 用户添加任务时，插件记录："用户在输入框中输入了：买菜"
5. 用户点击添加按钮时，插件记录："用户点击了添加按钮"
6. LLM收到反馈后可能会优化界面或添加新功能

### 场景2：创建问卷调查

**输入描述**：
```
创建一个用户体验调查问卷，包含单选题、多选题和开放性问题，最后显示提交结果
```

**选择模板**：测试问卷模板

**预期效果**：
1. 生成多步骤问卷界面
2. 用户选择答案时自动记录
3. LLM根据用户行为优化问题顺序
4. 自动生成个性化的结果页面

## 🔧 高级功能

### 自定义提示词模板

1. **编辑模板**：
   ```javascript
   // 在 utils/prompt-templates.js 中添加
   myCustomTemplate: {
     name: '我的模板',
     description: '自定义模板描述',
     template: '你是专业的开发者，请创建：{USER_TASK}',
     variables: ['USER_TASK']
   }
   ```

2. **使用模板**：
   - 重新加载插件
   - 在模板选择器中会出现新模板

### 网站适配扩展

1. **添加新网站**：
   ```javascript
   // 在 utils/site-configs.js 中添加
   'new-ai-site.com': {
     name: 'NewAI',
     isNewChatUrl: () => window.location.pathname === '/',
     inputSelector: '.chat-input',
     sendButtonSelector: '.send-btn',
     // ... 其他配置
   }
   ```

2. **更新权限**：
   ```json
   // 在 manifest.json 中添加
   "host_permissions": [
     "https://new-ai-site.com/*"
   ]
   ```

## 🐛 常见问题解决

### Q1: 插件图标是灰色的
**原因**：当前网站不在支持列表中
**解决**：访问支持的LLM网站（DeepSeek、Kimi、ChatGPT等）

### Q2: 点击"启动预见UI"没反应
**原因**：不在新对话页面
**解决**：确保URL是网站根路径，如 `https://chat.deepseek.com/`

### Q3: 用户操作没有被记录
**原因**：自动注入功能未启用
**解决**：在插件弹窗中开启"自动注入行为"开关

### Q4: 代码没有自动运行
**原因**：自动运行功能未启用
**解决**：在插件弹窗中开启"自动运行代码"开关

## 🎨 最佳实践

### 提示词编写技巧

1. **明确需求**：
   ```
   ❌ 创建一个网页
   ✅ 创建一个在线计算器，支持基本四则运算，界面采用网格布局
   ```

2. **指定交互**：
   ```
   ❌ 做一个游戏
   ✅ 创建一个猜数字游戏，用户输入数字后给出大小提示，猜对后显示祝贺
   ```

3. **描述样式**：
   ```
   ❌ 好看的界面
   ✅ 使用现代扁平化设计，蓝色主题，圆角按钮，响应式布局
   ```

### 交互设计建议

1. **清晰的操作反馈**：确保每个按钮点击都有明显效果
2. **合理的信息层次**：重要功能放在显眼位置
3. **友好的错误处理**：输入错误时给出明确提示
4. **渐进式功能**：从简单功能开始，逐步完善

## 📊 功能对比

| 传统方式 | 预见UI方式 |
|---------|-----------|
| 静态代码展示 | 动态交互应用 |
| 手动复制粘贴 | 自动运行渲染 |
| 单向生成 | 双向反馈 |
| 一次性结果 | 持续优化 |
| 需要技术背景 | 零技术门槛 |

## 🔮 应用场景

### 个人用户
- **学习工具**：创建练习题、记忆卡片
- **生活助手**：待办清单、记账工具
- **娱乐应用**：小游戏、测试问卷

### 教育领域
- **互动课件**：动态演示、实验模拟
- **在线测试**：自动评分、即时反馈
- **学习评估**：进度跟踪、能力测试

### 商业应用
- **原型设计**：快速验证想法
- **用户调研**：收集反馈数据
- **培训工具**：交互式培训材料

## 🚀 下一步

1. **熟练使用**：多尝试不同类型的应用创建
2. **探索模板**：尝试所有内置模板的效果
3. **自定义扩展**：根据需要添加新网站或模板
4. **分享经验**：与他人分享有趣的应用创意
5. **参与改进**：提供反馈和建议

## 📞 获取帮助

- **文档**：查看 README.md 和 DEVELOPMENT.md
- **测试**：使用 test/test-page.html 验证功能
- **调试**：查看浏览器控制台的日志信息
- **反馈**：提交Issue或Pull Request

---

**开始您的预见UI之旅吧！让AI生成的界面真正"活"起来！** 🎉

/**
 * 图标生成脚本
 * 用于从SVG生成不同尺寸的PNG图标
 * 
 * 使用方法：
 * 1. 安装依赖：npm install canvas
 * 2. 运行脚本：node scripts/generate-icons.js
 */

const fs = require('fs');
const path = require('path');

// 简单的SVG转Canvas实现（用于生成图标）
function generateIconHTML(size) {
  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { margin: 0; padding: 20px; background: #f0f0f0; }
        .icon-container { 
            display: inline-block; 
            margin: 10px; 
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .icon { 
            width: ${size}px; 
            height: ${size}px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: ${Math.round(size * 0.1875)}px;
            position: relative;
            display: inline-block;
        }
        .eye {
            position: absolute;
            top: ${Math.round(size * 0.25)}px;
            left: ${Math.round(size * 0.1875)}px;
            width: ${Math.round(size * 0.5625)}px;
            height: ${Math.round(size * 0.3125)}px;
            background: rgba(255,255,255,0.9);
            border-radius: 50%;
        }
        .pupil {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: ${Math.round(size * 0.1875)}px;
            height: ${Math.round(size * 0.1875)}px;
            background: #2c3e50;
            border-radius: 50%;
        }
        .highlight {
            position: absolute;
            top: 30%;
            right: 30%;
            width: ${Math.round(size * 0.0625)}px;
            height: ${Math.round(size * 0.0625)}px;
            background: white;
            border-radius: 50%;
        }
        .ui-elements {
            position: absolute;
            bottom: ${Math.round(size * 0.125)}px;
            left: ${Math.round(size * 0.15625)}px;
            right: ${Math.round(size * 0.15625)}px;
            height: ${Math.round(size * 0.09375)}px;
        }
        .ui-block {
            display: inline-block;
            height: 100%;
            background: rgba(255,255,255,0.8);
            border-radius: ${Math.round(size * 0.015625)}px;
            margin-right: ${Math.round(size * 0.03125)}px;
        }
        .ui-block:nth-child(1) { width: ${Math.round(size * 0.125)}px; }
        .ui-block:nth-child(2) { width: ${Math.round(size * 0.1875)}px; }
        .ui-block:nth-child(3) { width: ${Math.round(size * 0.15625)}px; }
        .ui-block:nth-child(4) { width: ${Math.round(size * 0.125)}px; }
    </style>
</head>
<body>
    <div class="icon-container">
        <div class="icon">
            <div class="eye">
                <div class="pupil">
                    <div class="highlight"></div>
                </div>
            </div>
            <div class="ui-elements">
                <div class="ui-block"></div>
                <div class="ui-block"></div>
                <div class="ui-block"></div>
                <div class="ui-block"></div>
            </div>
        </div>
        <div style="margin-top: 10px; font-size: 12px; color: #666;">
            ${size}x${size}px
        </div>
    </div>
</body>
</html>`;
}

// 生成图标预览页面
function generateIconPreview() {
  const sizes = [16, 32, 48, 128];
  const html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>预见UI插件图标预览</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0; 
            padding: 40px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 { 
            text-align: center; 
            color: #2c3e50; 
            margin-bottom: 40px;
        }
        .icons-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        .icon-container { 
            text-align: center;
            background: #f8f9fa;
            padding: 30px;
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: border-color 0.3s ease;
        }
        .icon-container:hover {
            border-color: #667eea;
        }
        .icon { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 18.75%;
            position: relative;
            display: inline-block;
            margin-bottom: 15px;
        }
        .eye {
            position: absolute;
            background: rgba(255,255,255,0.9);
            border-radius: 50%;
        }
        .pupil {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #2c3e50;
            border-radius: 50%;
        }
        .highlight {
            position: absolute;
            top: 30%;
            right: 30%;
            background: white;
            border-radius: 50%;
        }
        .ui-elements {
            position: absolute;
            bottom: 12.5%;
            left: 15.625%;
            right: 15.625%;
            height: 9.375%;
        }
        .ui-block {
            display: inline-block;
            height: 100%;
            background: rgba(255,255,255,0.8);
            border-radius: 1.5625%;
            margin-right: 3.125%;
        }
        .size-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }
        .usage {
            font-size: 12px;
            color: #6c757d;
        }
        .instructions {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .instructions h3 {
            margin-top: 0;
            color: #004085;
        }
        .instructions p {
            margin-bottom: 10px;
            color: #004085;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔮 预见UI插件图标预览</h1>
        
        <div class="icons-grid">
            ${sizes.map(size => `
                <div class="icon-container">
                    <div class="icon" style="width: ${size}px; height: ${size}px;">
                        <div class="eye" style="
                            top: ${Math.round(size * 0.25)}px;
                            left: ${Math.round(size * 0.1875)}px;
                            width: ${Math.round(size * 0.5625)}px;
                            height: ${Math.round(size * 0.3125)}px;
                        ">
                            <div class="pupil" style="
                                width: ${Math.round(size * 0.1875)}px;
                                height: ${Math.round(size * 0.1875)}px;
                            ">
                                <div class="highlight" style="
                                    width: ${Math.round(size * 0.0625)}px;
                                    height: ${Math.round(size * 0.0625)}px;
                                "></div>
                            </div>
                        </div>
                        <div class="ui-elements">
                            <div class="ui-block" style="width: ${Math.round(size * 0.125)}px;"></div>
                            <div class="ui-block" style="width: ${Math.round(size * 0.1875)}px;"></div>
                            <div class="ui-block" style="width: ${Math.round(size * 0.15625)}px;"></div>
                            <div class="ui-block" style="width: ${Math.round(size * 0.125)}px;"></div>
                        </div>
                    </div>
                    <div class="size-label">${size}×${size}px</div>
                    <div class="usage">${getUsageText(size)}</div>
                </div>
            `).join('')}
        </div>
        
        <div class="instructions">
            <h3>📝 使用说明</h3>
            <p><strong>图标设计理念：</strong></p>
            <ul>
                <li>🔮 <strong>眼睛符号</strong>：代表"预见"功能，象征洞察和预测</li>
                <li>🎨 <strong>UI元素</strong>：底部的小方块代表界面组件</li>
                <li>🌈 <strong>渐变背景</strong>：现代化的视觉效果</li>
                <li>✨ <strong>高光点</strong>：增加立体感和活力</li>
            </ul>
            <p><strong>技术实现：</strong></p>
            <ul>
                <li>使用CSS渐变和定位实现响应式图标</li>
                <li>支持多种尺寸，自动缩放</li>
                <li>兼容Chrome扩展的图标规范</li>
            </ul>
        </div>
    </div>
</body>
</html>`;

  return html;
}

function getUsageText(size) {
  switch(size) {
    case 16: return '工具栏小图标';
    case 32: return '扩展列表图标';
    case 48: return '扩展管理页面';
    case 128: return 'Chrome商店展示';
    default: return '通用图标';
  }
}

// 创建图标预览文件
try {
  const previewHTML = generateIconPreview();
  fs.writeFileSync(path.join(__dirname, '../assets/icons/preview.html'), previewHTML);
  console.log('✅ 图标预览页面已生成: assets/icons/preview.html');
  
  // 生成各个尺寸的HTML文件（用于截图生成PNG）
  const sizes = [16, 32, 48, 128];
  sizes.forEach(size => {
    const iconHTML = generateIconHTML(size);
    fs.writeFileSync(path.join(__dirname, `../assets/icons/icon${size}.html`), iconHTML);
  });
  
  console.log('✅ 各尺寸图标HTML已生成');
  console.log('💡 提示：打开 assets/icons/preview.html 查看图标效果');
  console.log('💡 提示：可以使用浏览器截图工具将各个尺寸的图标保存为PNG文件');
  
} catch (error) {
  console.error('❌ 生成图标文件时出错:', error);
}

/**
 * 插件验证脚本
 * 检查插件文件的完整性和配置正确性
 */

const fs = require('fs');
const path = require('path');

// 颜色输出函数
const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`
};

console.log(colors.cyan('🔍 预见UI插件验证开始...\n'));

let errors = [];
let warnings = [];
let success = [];

/**
 * 检查文件是否存在
 */
function checkFileExists(filePath, description) {
  const fullPath = path.join(__dirname, '..', filePath);
  if (fs.existsSync(fullPath)) {
    success.push(`✅ ${description}: ${filePath}`);
    return true;
  } else {
    errors.push(`❌ ${description}缺失: ${filePath}`);
    return false;
  }
}

/**
 * 检查JSON文件格式
 */
function checkJSONFile(filePath, description) {
  const fullPath = path.join(__dirname, '..', filePath);
  try {
    const content = fs.readFileSync(fullPath, 'utf8');
    JSON.parse(content);
    success.push(`✅ ${description}格式正确: ${filePath}`);
    return true;
  } catch (error) {
    errors.push(`❌ ${description}格式错误: ${filePath} - ${error.message}`);
    return false;
  }
}

/**
 * 检查manifest.json配置
 */
function checkManifest() {
  const manifestPath = path.join(__dirname, '..', 'manifest.json');
  if (!fs.existsSync(manifestPath)) {
    errors.push('❌ manifest.json文件不存在');
    return;
  }

  try {
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    
    // 检查必需字段
    const requiredFields = ['manifest_version', 'name', 'version', 'description'];
    requiredFields.forEach(field => {
      if (manifest[field]) {
        success.push(`✅ manifest.json包含${field}字段`);
      } else {
        errors.push(`❌ manifest.json缺少${field}字段`);
      }
    });

    // 检查图标文件
    if (manifest.icons) {
      Object.entries(manifest.icons).forEach(([size, iconPath]) => {
        checkFileExists(iconPath, `${size}x${size}图标`);
      });
    }

    // 检查content scripts文件
    if (manifest.content_scripts) {
      manifest.content_scripts.forEach((script, index) => {
        if (script.js) {
          script.js.forEach(jsFile => {
            checkFileExists(jsFile, `Content Script ${index + 1}`);
          });
        }
      });
    }

    // 检查background script
    if (manifest.background && manifest.background.service_worker) {
      checkFileExists(manifest.background.service_worker, 'Background Service Worker');
    }

    // 检查popup文件
    if (manifest.action && manifest.action.default_popup) {
      checkFileExists(manifest.action.default_popup, 'Popup HTML');
    }

  } catch (error) {
    errors.push(`❌ manifest.json解析错误: ${error.message}`);
  }
}

/**
 * 检查核心文件
 */
function checkCoreFiles() {
  console.log(colors.blue('📋 检查核心文件...'));
  
  // 必需的核心文件
  const coreFiles = [
    { path: 'manifest.json', desc: 'Manifest配置文件', type: 'json' },
    { path: 'popup/popup.html', desc: 'Popup HTML文件' },
    { path: 'popup/popup.css', desc: 'Popup CSS文件' },
    { path: 'popup/popup.js', desc: 'Popup JavaScript文件' },
    { path: 'background/service-worker.js', desc: 'Background Service Worker' },
    { path: 'content/main-controller.js', desc: '主控制器' },
    { path: 'content/llm-detector.js', desc: 'LLM检测器' },
    { path: 'content/behavior-tracker.js', desc: '行为追踪器' },
    { path: 'content/auto-injector.js', desc: '自动注入器' },
    { path: 'content/code-monitor.js', desc: '代码监控器' },
    { path: 'utils/site-configs.js', desc: '网站配置' },
    { path: 'utils/message-bridge.js', desc: '消息桥接器' },
    { path: 'utils/prompt-templates.js', desc: '提示词模板' }
  ];

  coreFiles.forEach(file => {
    if (file.type === 'json') {
      checkJSONFile(file.path, file.desc);
    } else {
      checkFileExists(file.path, file.desc);
    }
  });
}

/**
 * 检查图标文件
 */
function checkIcons() {
  console.log(colors.blue('🎨 检查图标文件...'));
  
  const iconSizes = [16, 32, 48, 128];
  iconSizes.forEach(size => {
    checkFileExists(`assets/icons/icon${size}.png`, `${size}x${size}PNG图标`);
  });
}

/**
 * 检查文档文件
 */
function checkDocumentation() {
  console.log(colors.blue('📚 检查文档文件...'));
  
  const docFiles = [
    { path: 'README.md', desc: '项目说明文档' },
    { path: 'INSTALL.md', desc: '安装指南' },
    { path: 'DEVELOPMENT.md', desc: '开发文档' },
    { path: 'QUICK_START.md', desc: '快速开始指南' },
    { path: 'CHANGELOG.md', desc: '更新日志' },
    { path: 'LICENSE', desc: '许可证文件' }
  ];

  docFiles.forEach(file => {
    checkFileExists(file.path, file.desc);
  });
}

/**
 * 检查可选文件
 */
function checkOptionalFiles() {
  console.log(colors.blue('🔧 检查可选文件...'));
  
  const optionalFiles = [
    { path: 'test/test-page.html', desc: '测试页面' },
    { path: 'assets/icons/preview.html', desc: '图标预览页面' },
    { path: 'assets/icons/create-icons.html', desc: '图标生成器' },
    { path: 'scripts/generate-icons.js', desc: '图标生成脚本' }
  ];

  optionalFiles.forEach(file => {
    if (checkFileExists(file.path, file.desc)) {
      // 文件存在，这是好的
    } else {
      warnings.push(`⚠️ 可选文件缺失: ${file.path}`);
    }
  });
}

/**
 * 输出验证结果
 */
function outputResults() {
  console.log('\n' + colors.cyan('📊 验证结果汇总:'));
  console.log('='.repeat(50));
  
  if (success.length > 0) {
    console.log(colors.green('\n✅ 成功项目:'));
    success.forEach(item => console.log(`  ${item}`));
  }
  
  if (warnings.length > 0) {
    console.log(colors.yellow('\n⚠️ 警告项目:'));
    warnings.forEach(item => console.log(`  ${item}`));
  }
  
  if (errors.length > 0) {
    console.log(colors.red('\n❌ 错误项目:'));
    errors.forEach(item => console.log(`  ${item}`));
  }
  
  console.log('\n' + '='.repeat(50));
  console.log(colors.cyan(`📈 统计: ${colors.green(success.length + '个成功')} | ${colors.yellow(warnings.length + '个警告')} | ${colors.red(errors.length + '个错误')}`));
  
  if (errors.length === 0) {
    console.log(colors.green('\n🎉 插件验证通过！可以在Chrome中加载使用。'));
    console.log(colors.blue('\n📝 下一步操作:'));
    console.log('  1. 打开Chrome浏览器');
    console.log('  2. 访问 chrome://extensions/');
    console.log('  3. 开启开发者模式');
    console.log('  4. 点击"加载已解压的扩展程序"');
    console.log('  5. 选择插件根目录');
    console.log('  6. 开始使用预见UI插件！');
  } else {
    console.log(colors.red('\n🚨 插件验证失败！请修复上述错误后重试。'));
  }
}

// 执行验证
try {
  checkCoreFiles();
  checkIcons();
  checkManifest();
  checkDocumentation();
  checkOptionalFiles();
  outputResults();
} catch (error) {
  console.error(colors.red(`\n💥 验证过程中发生错误: ${error.message}`));
  process.exit(1);
}

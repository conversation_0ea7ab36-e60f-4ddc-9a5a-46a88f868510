# 预见UI - LLM交互增强插件

一款革命性的Chrome插件，让LLM生成的网页与用户实现真正的交互。通过监控用户在渲染页面上的操作行为，自动生成反馈并发送给LLM，实现持续的界面优化和功能完善。

## ✨ 核心功能

### 🎯 智能交互监控
- **实时行为追踪**：监控用户在LLM生成页面上的所有操作（点击、输入、滚动等）
- **自然语言描述**：将用户行为转换为自然语言描述，便于LLM理解
- **自动反馈注入**：将用户操作自动发送到LLM对话框，实现持续交互

### 🚀 一键启动预见UI
- **智能网站检测**：自动识别支持的LLM网站（DeepSeek、Kimi、ChatGPT等）
- **新对话识别**：检测新对话页面，提供快速启动入口
- **提示词模板**：内置多种场景模板，支持自定义
- **任务描述输入**：用户只需描述想要的应用，插件自动生成完整提示词

### 🤖 自动化流程
- **代码块检测**：自动识别LLM生成的代码块和运行按钮
- **自动运行**：可选择自动点击运行按钮，无需手动操作
- **渲染监控**：监控页面渲染完成，自动启动行为追踪
- **持续优化**：基于用户反馈，LLM持续改进界面

## 🌐 支持的网站

- **DeepSeek** (chat.deepseek.com)
- **Kimi** (kimi.moonshot.cn)
- **ChatGPT** (chatgpt.com)
- **Claude** (claude.ai)
- **Gemini** (gemini.google.com)
- **通义千问** (tongyi.aliyun.com)

## 📦 安装方法

### 开发者模式安装
1. 下载或克隆此项目到本地
2. 打开Chrome浏览器，进入 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹
6. 插件安装完成！

### 使用方法
1. 访问支持的LLM网站
2. 开启新对话
3. 点击插件图标，输入想要创建的应用描述
4. 点击"启动预见UI"
5. 等待LLM生成代码并自动运行
6. 在渲染的页面上进行操作，插件会自动反馈给LLM

## 🎮 使用示例

### 创建MBTI测试应用
1. 在DeepSeek开启新对话
2. 点击插件，输入："创建一个MBTI性格测试页面"
3. 选择"测试问卷模板"
4. 点击"启动预见UI"
5. LLM会生成完整的MBTI测试页面
6. 用户答题时，插件自动记录选择并反馈给LLM
7. LLM根据反馈优化测试流程和结果展示

### 创建计算器工具
1. 输入："创建一个科学计算器"
2. 选择"工具模板"
3. LLM生成计算器界面
4. 用户点击按钮时，插件记录操作
5. LLM根据使用习惯优化按钮布局和功能

## 🔧 技术架构

### 模块化设计
```
├── manifest.json           # 插件配置
├── background/             # 后台服务
├── content/               # 内容脚本
│   ├── llm-detector.js    # LLM网站检测
│   ├── code-monitor.js    # 代码块监控
│   ├── behavior-tracker.js # 行为追踪
│   └── auto-injector.js   # 自动注入
├── popup/                 # 弹窗界面
├── utils/                 # 工具模块
│   ├── site-configs.js    # 网站配置
│   ├── prompt-templates.js # 提示词模板
│   └── message-bridge.js  # 消息桥接
```

### 核心特性
- **高内聚低耦合**：模块化设计，便于维护和扩展
- **配置驱动**：通过配置文件支持新网站，无需修改核心代码
- **消息机制**：统一的消息传递系统，确保组件间通信可靠
- **错误处理**：完善的错误处理和日志记录
- **性能优化**：防抖、节流等技术，确保流畅体验

## 🎨 提示词模板

### 内置模板
- **默认模板**：基础交互式UI生成
- **交互式模板**：强调用户交互和反馈
- **简单模板**：快速原型设计
- **游戏模板**：游戏类应用专用
- **工具模板**：实用工具类应用
- **表单模板**：数据收集类应用
- **可视化模板**：数据展示和图表
- **测试模板**：问卷调查类应用

### 自定义模板
支持用户创建和导入自定义提示词模板，满足特定需求。

## ⚙️ 配置选项

- **自动运行代码**：自动点击代码块运行按钮
- **自动注入行为**：自动将用户操作发送给LLM
- **注入延迟**：控制行为反馈的频率
- **监控范围**：选择要监控的操作类型

## 🔮 未来规划

- **更多LLM网站支持**：扩展到更多AI平台
- **智能行为分析**：基于AI的用户意图识别
- **协作功能**：多用户协同设计界面
- **模板市场**：社区共享提示词模板
- **可视化编辑器**：拖拽式界面设计
- **API集成**：支持外部数据源和服务

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 开发环境
1. 克隆项目
2. 安装依赖（如有）
3. 在Chrome中加载插件
4. 开始开发

### 代码规范
- 使用中文注释
- 遵循模块化设计原则
- 添加适当的日志记录
- 编写测试用例

## 📄 许可证

MIT License

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

**预见UI** - 让AI生成的界面真正"活"起来！
